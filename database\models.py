#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Conversation(Base):
    """对话记录表"""
    __tablename__ = 'conversations'

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, index=True, comment='会话ID - 用于逻辑分组和主题区分')
    role = Column(String(50), nullable=False, comment='角色：user/assistant/system')
    content = Column(Text, nullable=False, comment='对话内容')
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, comment='对话发生时间')
    attachments = Column(JSON, comment='附件信息数组：[{"path": "/uploads/file.jpg", "size": 1024, "name": "screenshot.png", "mime_type": "image/png"}]')
    meta_data = Column(JSON, comment='元数据：模型参数、token数、处理时间等')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='记录创建时间（用于审计）')

    def __repr__(self):
        return f"<Conversation(id={self.id}, session_id='{self.session_id}', role='{self.role}')>"


class SystemMetrics(Base):
    """系统监控数据表"""
    __tablename__ = 'system_metrics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, comment='采集时间')
    cpu_percent = Column(Float, comment='CPU使用率(%)')
    memory_percent = Column(Float, comment='内存使用率(%)')
    memory_used = Column(Float, comment='已用内存(MB)')
    memory_total = Column(Float, comment='总内存(MB)')
    disk_percent = Column(Float, comment='磁盘使用率(%)')
    disk_used = Column(Float, comment='已用磁盘(GB)')
    disk_total = Column(Float, comment='总磁盘(GB)')
    network_sent = Column(Float, comment='网络发送(MB)')
    network_recv = Column(Float, comment='网络接收(MB)')
    process_count = Column(Integer, comment='进程数量')
    load_average = Column(Float, comment='系统负载')
    additional_data = Column(JSON, comment='额外监控数据')
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<SystemMetrics(id={self.id}, timestamp='{self.timestamp}', cpu={self.cpu_percent}%)>"


class LogEntry(Base):
    """日志记录表"""
    __tablename__ = 'logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, comment='日志时间')
    level = Column(String(20), nullable=False, index=True, comment='日志级别：DEBUG/INFO/WARNING/ERROR/CRITICAL')
    module = Column(String(100), nullable=False, index=True, comment='模块名称')
    message = Column(Text, nullable=False, comment='日志消息')
    function_name = Column(String(100), comment='函数名称')
    line_number = Column(Integer, comment='行号')
    exception_info = Column(Text, comment='异常信息')
    extra_data = Column(JSON, comment='额外数据')
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<LogEntry(id={self.id}, level='{self.level}', module='{self.module}')>"


class Memory(Base):
    """记忆数据表（为记忆模块预留）"""
    __tablename__ = 'memories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    content = Column(Text, nullable=False, comment='记忆内容')
    memory_type = Column(String(50), nullable=False, index=True, comment='记忆类型：semantic/episodic/procedural')
    importance = Column(Float, default=1.0, comment='重要性评分(0-10)')
    source_type = Column(String(50), comment='来源类型：conversation/system/manual')
    source_id = Column(String(255), comment='来源ID')
    tags = Column(JSON, comment='标签列表')
    embedding = Column(JSON, comment='向量嵌入（预留）')
    access_count = Column(Integer, default=0, comment='访问次数')
    last_accessed = Column(DateTime, comment='最后访问时间')
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<Memory(id={self.id}, type='{self.memory_type}', importance={self.importance})>"


class DatabaseConfig(Base):
    """数据库配置表"""
    __tablename__ = 'config'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(255), nullable=False, unique=True, comment='配置键')
    value = Column(Text, comment='配置值')
    description = Column(Text, comment='配置描述')
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<DatabaseConfig(key='{self.key}', value='{self.value}')>"


# 数据模型映射字典，便于动态操作
MODEL_MAPPING = {
    'conversations': Conversation,
    'system_metrics': SystemMetrics,
    'logs': LogEntry,
    'memories': Memory,
    'config': DatabaseConfig
}

# 表名列表
TABLE_NAMES = list(MODEL_MAPPING.keys())
