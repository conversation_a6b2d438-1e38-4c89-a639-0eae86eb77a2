# Yuan项目微服务开发规范

## 总体原则

Yuan项目采用基于服务注册中心的微服务架构，每个服务必须遵循以下开发规范，确保服务间的一致性和可维护性。

## 服务结构规范

### 目录结构
```
{service_name}_service/
├── __init__.py
├── main.py                 # 服务入口文件
├── service.py              # 服务主类
├── models.py               # 数据库模型
├── schemas.py              # Pydantic模型
├── config.py               # 配置管理
├── tests/                  # 测试文件
│   ├── __init__.py
│   ├── test_service.py
│   └── test_api.py
└── README.md               # 服务文档
```

### 服务命名规范
- **服务名称**：小写字母，使用下划线分隔，如`chat_service`、`log_service`
- **目录名称**：与服务名称一致，如`chat_service/`
- **数据库文件**：`data/{service_name}_service.db`
- **端口分配**：按功能分配固定端口号

## 服务基础实现

### 1. 继承BaseService
所有微服务必须继承`BaseService`类：

```python
from microservices_v2.base_service import BaseService

class YourService(BaseService):
    def __init__(self):
        super().__init__("your_service", 8007)  # 服务名和端口
        self.setup_database()
        self.setup_routes()
```

### 2. 服务注册信息
服务启动时自动注册，必须提供以下信息：

```python
{
    "service_name": "your_service",           # 服务名称
    "host": "127.0.0.1",                     # 服务IP
    "port": 8007,                            # 服务端口
    "health_check_url": "http://127.0.0.1:8007/health",
    "metadata": {
        "version": "1.0.0",                  # 服务版本
        "description": "服务描述",            # 服务描述
        "capabilities": ["功能1", "功能2"]    # 服务能力
    }
}
```

### 3. 必需端点
每个服务必须实现以下标准端点：

```python
@self.app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": self.service_name,
        "timestamp": time.time()
    }

@self.app.get("/info")
async def service_info():
    """服务信息端点"""
    return {
        "service_name": self.service_name,
        "version": "1.0.0",
        "capabilities": ["功能列表"]
    }
```

## 数据库设计规范

### 1. 独立数据库原则
- 每个服务使用独立的SQLite数据库
- 数据库文件存储在`data/`目录下
- 命名格式：`{service_name}_service.db`
- 服务间不得直接访问其他服务的数据库

### 2. 数据库模型定义
使用SQLAlchemy定义数据库模型：

```python
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class YourModel(Base):
    __tablename__ = 'your_table'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment='名称')
    content = Column(Text, comment='内容')
    metadata = Column(JSON, comment='元数据')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
```

### 3. 数据库初始化
在服务启动时初始化数据库：

```python
async def initialize_database(self):
    """初始化数据库"""
    async with self.async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print(f"✅ {self.service_name} 数据库初始化完成")
```

## API设计规范

### 1. RESTful设计原则
- 使用标准HTTP方法：GET、POST、PUT、DELETE
- 资源导向的URL设计
- 使用HTTP状态码表示操作结果
- 支持查询参数和分页

### 2. 统一响应格式
成功响应格式：
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

错误响应格式：
```json
{
    "success": false,
    "error": "错误类型",
    "message": "错误描述",
    "details": {},
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3. 请求/响应模型
使用Pydantic定义请求和响应模型：

```python
from pydantic import BaseModel
from typing import Optional, List

class CreateRequest(BaseModel):
    name: str
    content: str
    metadata: Optional[dict] = None

class ItemResponse(BaseModel):
    id: int
    name: str
    content: str
    created_at: str
```

## 服务间通信规范

### 1. 服务发现
使用BaseService提供的服务发现方法：

```python
# 发现其他服务
services = await self.discover_service("target_service")
if not services:
    raise Exception("目标服务不可用")
```

### 2. API调用
使用BaseService提供的API调用方法：

```python
# 调用其他服务
result = await self.call_service(
    service_name="log_service",
    endpoint="/logs",
    method="POST",
    data={
        "level": "INFO",
        "message": "操作日志"
    }
)
```

### 3. 错误处理
实现完善的错误处理机制：

```python
try:
    result = await self.call_service("target_service", "/api", "POST", data)
    return result
except Exception as e:
    # 记录错误日志
    await self.call_service("log_service", "/logs", "POST", {
        "level": "ERROR",
        "message": f"调用服务失败: {str(e)}"
    })
    raise HTTPException(status_code=500, detail="服务调用失败")
```

## 配置管理规范

### 1. 配置文件结构
```python
# config.py
from pydantic import BaseSettings

class ServiceConfig(BaseSettings):
    service_name: str = "your_service"
    service_port: int = 8007
    registry_url: str = "http://127.0.0.1:8500"
    database_url: str = "sqlite+aiosqlite:///data/your_service.db"
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        env_prefix = "YOUR_SERVICE_"
```

### 2. 环境变量支持
支持通过环境变量覆盖配置：
```bash
export YOUR_SERVICE_PORT=8008
export YOUR_SERVICE_LOG_LEVEL=DEBUG
```

## 日志记录规范

### 1. 日志级别
- **DEBUG**：调试信息
- **INFO**：一般信息
- **WARNING**：警告信息
- **ERROR**：错误信息
- **CRITICAL**：严重错误

### 2. 日志记录方式
通过Log Service记录日志：

```python
await self.call_service("log_service", "/logs", "POST", {
    "level": "INFO",
    "module": self.service_name,
    "message": "操作描述",
    "function_name": "function_name",
    "extra_data": {"key": "value"}
})
```

## 测试规范

### 1. 单元测试
每个服务必须包含单元测试：

```python
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_health_check():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
```

### 2. 集成测试
测试服务间的集成：

```python
@pytest.mark.asyncio
async def test_service_integration():
    # 测试服务注册
    # 测试服务发现
    # 测试API调用
    pass
```

## 部署规范

### 1. 服务启动
每个服务提供标准的启动脚本：

```python
# main.py
if __name__ == "__main__":
    service = YourService()
    service.run()
```

### 2. 健康检查
服务必须响应健康检查请求，用于监控和负载均衡。

### 3. 优雅关闭
服务关闭时必须：
- 停止接受新请求
- 完成正在处理的请求
- 从服务注册中心注销
- 清理资源和连接

## 监控和运维

### 1. 性能指标
记录关键性能指标：
- 请求响应时间
- 请求成功率
- 数据库连接数
- 内存使用情况

### 2. 告警机制
设置关键指标的告警阈值：
- 服务不可用
- 响应时间过长
- 错误率过高
- 资源使用异常

## 安全规范

### 1. 输入验证
- 使用Pydantic验证输入数据
- 防止SQL注入
- 限制请求大小

### 2. 访问控制
- 实现服务间认证
- 限制敏感操作
- 记录访问日志

### 3. 数据保护
- 敏感数据加密存储
- 传输过程加密
- 定期备份数据

---

遵循以上规范，确保Yuan项目微服务架构的一致性、可维护性和可扩展性。
