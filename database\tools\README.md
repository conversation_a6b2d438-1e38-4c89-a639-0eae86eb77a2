# 数据库开发工具

Yuan项目数据库模块的开发工具集，包含命令行工具和Web可视化工具。

## 工具概述

### 1. 命令行工具 (CLI Tool)
- **文件**: `cli_tool.py`
- **用途**: 适合AI和脚本化操作
- **特点**: 支持多种输出格式、批量操作、自动化脚本

### 2. Web可视化工具 (Web Tool)
- **文件**: `web_tool.py`
- **用途**: 适合用户可视化操作
- **特点**: 直观的Web界面、实时数据展示、图表统计

## 快速开始

### 安装依赖
```bash
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 安装额外依赖
pip install tabulate flask
```

### 命令行工具使用

#### 查看帮助
```bash
python -m database.tools.cli_tool --help
```

#### 查看数据库状态
```bash
# 表格格式
python -m database.tools.cli_tool --status

# JSON格式
python -m database.tools.cli_tool --status --format json

# CSV格式
python -m database.tools.cli_tool --status --format csv
```

#### 查询数据
```bash
# 查询对话记录
python -m database.tools.cli_tool --table conversations --limit 10

# 按会话ID查询
python -m database.tools.cli_tool --table conversations --session-id "test_001"

# 查询日志记录
python -m database.tools.cli_tool --table logs --level ERROR --hours 24

# 执行自定义SQL查询
python -m database.tools.cli_tool --query "SELECT * FROM conversations ORDER BY timestamp DESC LIMIT 5"
```

#### 插入数据
```bash
# 插入对话记录
python -m database.tools.cli_tool --insert-conversation \
  --session-id "test_001" \
  --role "user" \
  --content "你好，这是测试消息" \
  --metadata '{"model": "gpt-4", "temperature": 0.7}'

# 插入日志记录
python -m database.tools.cli_tool --insert-log \
  --level "INFO" \
  --module "test_module" \
  --message "测试日志消息" \
  --function "test_function" \
  --line 123
```

#### 导出数据
```bash
# 导出对话记录
python -m database.tools.cli_tool --export conversations --limit 1000 --filename "conversations_backup.json"

# 导出日志记录
python -m database.tools.cli_tool --export logs --limit 500
```

#### 维护操作
```bash
# 重建索引
python -m database.tools.cli_tool --maintenance reindex

# 分析表统计
python -m database.tools.cli_tool --maintenance analyze

# 完整性检查
python -m database.tools.cli_tool --maintenance integrity

# 清理旧数据（需要确认）
python -m database.tools.cli_tool --cleanup 30 --confirm
```

### Web工具使用

#### 启动Web服务
```bash
# 默认端口5000
python -m database.tools.web_tool

# 指定端口
python -m database.tools.web_tool --port 5001

# 调试模式
python -m database.tools.web_tool --debug
```

#### 访问Web界面
打开浏览器访问: http://127.0.0.1:5000

#### Web功能说明

**仪表板页面**:
- 数据库状态概览
- 表记录统计图表
- 实时状态监控

**数据表页面**:
- 浏览所有数据表
- 过滤和搜索数据
- 新增数据记录
- 导出表数据

**查询工具页面**:
- SQL查询编辑器
- 查询模板快速选择
- 查询结果展示
- 查询历史记录

**维护工具**:
- 数据库维护操作
- 旧数据清理
- 索引重建等

## 输出格式说明

### 命令行工具支持三种输出格式:

1. **table** (默认): 表格格式，适合人类阅读
2. **json**: JSON格式，适合程序处理
3. **csv**: CSV格式，适合导入Excel等工具

### 示例输出

**Table格式**:
```
+----------------+-------+
| 表名             |   记录数 |
+================+=======+
| conversations  |   150 |
+----------------+-------+
| logs           |   320 |
+----------------+-------+
```

**JSON格式**:
```json
{
  "table_stats": {
    "conversations": 150,
    "logs": 320
  },
  "total_records": 470
}
```

## 使用场景

### AI助手使用命令行工具
```bash
# 快速查看数据库状态
python -m database.tools.cli_tool --status --format json

# 查询最近的对话记录
python -m database.tools.cli_tool --table conversations --limit 5 --format json

# 插入测试数据
python -m database.tools.cli_tool --insert-conversation --session-id "ai_test" --role "user" --content "测试消息"
```

### 用户使用Web工具
1. 启动Web服务: `python -m database.tools.web_tool`
2. 打开浏览器访问管理界面
3. 通过图形界面进行数据管理

## 注意事项

1. **安全性**: 命令行工具只支持SELECT查询，Web工具有完整的权限控制
2. **性能**: 大量数据查询时建议使用limit限制结果数量
3. **备份**: 执行清理操作前建议先导出数据备份
4. **并发**: Web工具支持多用户同时访问
5. **日志**: 所有操作都会记录到数据库日志表中

## 故障排除

### 常见问题

1. **数据库文件不存在**
   - 工具会自动创建数据库文件和表结构

2. **端口被占用**
   - 使用 `--port` 参数指定其他端口

3. **权限问题**
   - 确保对数据库文件目录有读写权限

4. **依赖缺失**
   - 运行 `pip install tabulate flask` 安装依赖

### 获取帮助
```bash
# 查看命令行工具帮助
python -m database.tools.cli_tool --help

# 查看Web工具帮助
python -m database.tools.web_tool --help

# 列出所有可用工具
python -m database.tools
```

## 开发说明

### 目录结构
```
database/tools/
├── __init__.py          # 工具模块初始化
├── cli_tool.py          # 命令行工具
├── web_tool.py          # Web工具
├── templates/           # Web模板
│   ├── base.html       # 基础模板
│   ├── index.html      # 仪表板
│   ├── tables.html     # 数据表页面
│   └── query.html      # 查询工具页面
├── static/             # 静态文件（预留）
└── README.md           # 本文档
```

### 扩展开发
- 命令行工具: 在 `DatabaseCLI` 类中添加新方法
- Web工具: 在 `DatabaseWebTool` 类中添加新路由
- 模板: 在 `templates/` 目录中添加新页面

## 版本信息
- 版本: 1.0.0
- 作者: Yuan Project
- 更新时间: 2025-06-13
