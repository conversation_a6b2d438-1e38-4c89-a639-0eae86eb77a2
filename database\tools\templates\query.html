{% extends "base.html" %}

{% block title %}查询工具 - Yuan数据库管理工具{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-search text-primary"></i>
        SQL查询工具
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-outline-success btn-custom me-2" onclick="executeQuery()">
            <i class="fas fa-play"></i>
            执行查询
        </button>
        <button class="btn btn-outline-secondary btn-custom" onclick="clearQuery()">
            <i class="fas fa-eraser"></i>
            清空
        </button>
    </div>
</div>

<!-- 查询编辑器 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-code"></i>
                    SQL查询编辑器
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">SQL查询语句 (仅支持SELECT查询)</label>
                    <textarea class="form-control" id="sqlQuery" rows="8" placeholder="输入您的SQL查询语句...&#10;&#10;示例:&#10;SELECT * FROM conversations WHERE session_id = 'test_001' ORDER BY timestamp DESC LIMIT 10;&#10;&#10;SELECT level, COUNT(*) as count FROM logs GROUP BY level;&#10;&#10;SELECT DATE(timestamp) as date, COUNT(*) as count FROM conversations GROUP BY DATE(timestamp);"></textarea>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">快速查询模板</label>
                            <select class="form-select" id="queryTemplate" onchange="loadTemplate()">
                                <option value="">选择查询模板...</option>
                                <option value="conversations_recent">最近对话记录</option>
                                <option value="conversations_by_session">按会话查询对话</option>
                                <option value="logs_by_level">按级别统计日志</option>
                                <option value="logs_recent_errors">最近错误日志</option>
                                <option value="metrics_recent">最近系统监控</option>
                                <option value="table_stats">表记录统计</option>
                                <option value="daily_conversations">每日对话统计</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">可用表</label>
                            <div class="d-flex flex-wrap gap-2">
                                {% for table in tables %}
                                <span class="badge bg-secondary" style="cursor: pointer;" onclick="insertTableName('{{ table }}')">
                                    {{ table }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            提示: 点击表名可快速插入到查询中
                        </small>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="executeQuery()">
                            <i class="fas fa-play"></i>
                            执行查询
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查询结果 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i>
                    查询结果
                </h6>
                <div>
                    <button class="btn btn-sm btn-outline-info" onclick="exportResults()" id="exportResultsBtn" disabled>
                        <i class="fas fa-download"></i>
                        导出结果
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="queryResults">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>执行查询以查看结果</p>
                    </div>
                </div>
                
                <!-- 查询统计信息 -->
                <div class="mt-3" id="queryStats" style="display: none;">
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-list"></i>
                                记录数: <span id="recordCount">0</span>
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                执行时间: <span id="executionTime">0ms</span>
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i>
                                查询时间: <span id="queryTime"></span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查询历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history"></i>
                    查询历史
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group" id="queryHistory">
                    <div class="text-center text-muted py-3">
                        <p>暂无查询历史</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let queryResults = [];
let queryHistory = JSON.parse(localStorage.getItem('queryHistory') || '[]');

// 页面加载时显示查询历史
document.addEventListener('DOMContentLoaded', function() {
    displayQueryHistory();
});

// 查询模板
const queryTemplates = {
    conversations_recent: "SELECT * FROM conversations ORDER BY timestamp DESC LIMIT 20;",
    conversations_by_session: "SELECT * FROM conversations WHERE session_id = 'YOUR_SESSION_ID' ORDER BY timestamp;",
    logs_by_level: "SELECT level, COUNT(*) as count FROM logs GROUP BY level ORDER BY count DESC;",
    logs_recent_errors: "SELECT * FROM logs WHERE level IN ('ERROR', 'CRITICAL') ORDER BY timestamp DESC LIMIT 10;",
    metrics_recent: "SELECT * FROM system_metrics ORDER BY timestamp DESC LIMIT 10;",
    table_stats: `SELECT 
    'conversations' as table_name, COUNT(*) as record_count FROM conversations
UNION ALL SELECT 
    'logs' as table_name, COUNT(*) as record_count FROM logs
UNION ALL SELECT 
    'system_metrics' as table_name, COUNT(*) as record_count FROM system_metrics
UNION ALL SELECT 
    'memories' as table_name, COUNT(*) as record_count FROM memories
UNION ALL SELECT 
    'config' as table_name, COUNT(*) as record_count FROM config;`,
    daily_conversations: `SELECT 
    DATE(timestamp) as date, 
    COUNT(*) as conversation_count,
    COUNT(DISTINCT session_id) as unique_sessions
FROM conversations 
GROUP BY DATE(timestamp) 
ORDER BY date DESC 
LIMIT 7;`
};

// 加载查询模板
function loadTemplate() {
    const template = document.getElementById('queryTemplate').value;
    if (template && queryTemplates[template]) {
        document.getElementById('sqlQuery').value = queryTemplates[template];
    }
}

// 插入表名
function insertTableName(tableName) {
    const queryTextarea = document.getElementById('sqlQuery');
    const cursorPos = queryTextarea.selectionStart;
    const textBefore = queryTextarea.value.substring(0, cursorPos);
    const textAfter = queryTextarea.value.substring(cursorPos);
    
    queryTextarea.value = textBefore + tableName + textAfter;
    queryTextarea.focus();
    queryTextarea.setSelectionRange(cursorPos + tableName.length, cursorPos + tableName.length);
}

// 执行查询
function executeQuery() {
    const sql = document.getElementById('sqlQuery').value.trim();
    
    if (!sql) {
        showAlert('warning', '请输入SQL查询语句');
        return;
    }
    
    if (!sql.toUpperCase().startsWith('SELECT')) {
        showAlert('danger', '只支持SELECT查询语句');
        return;
    }
    
    const startTime = Date.now();
    showLoading();
    
    fetch('/api/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql: sql })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        const executionTime = Date.now() - startTime;
        
        if (data.error) {
            showAlert('danger', '查询错误: ' + data.error);
            displayQueryResults([], executionTime);
        } else {
            queryResults = data.data;
            displayQueryResults(data.data, executionTime);
            addToQueryHistory(sql, data.count, executionTime);
            showAlert('success', `查询成功，返回 ${data.count} 条记录`);
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('danger', '查询失败: ' + error.message);
        displayQueryResults([], Date.now() - startTime);
    });
}

// 显示查询结果
function displayQueryResults(data, executionTime) {
    const resultsDiv = document.getElementById('queryResults');
    const statsDiv = document.getElementById('queryStats');
    const exportBtn = document.getElementById('exportResultsBtn');
    
    if (!data || data.length === 0) {
        resultsDiv.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <p>查询无结果</p>
            </div>
        `;
        statsDiv.style.display = 'none';
        exportBtn.disabled = true;
        return;
    }
    
    // 生成表格
    const headers = Object.keys(data[0]);
    const tableHtml = `
        <div class="table-responsive">
            <table class="table table-hover table-striped">
                <thead class="table-dark">
                    <tr>
                        ${headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${data.map(row => `
                        <tr>
                            ${headers.map(header => {
                                let value = row[header];
                                if (value === null || value === undefined) {
                                    value = '<span class="text-muted">NULL</span>';
                                } else if (typeof value === 'string' && value.length > 100) {
                                    value = `<span title="${value}" class="text-truncate d-inline-block" style="max-width: 200px;">${value}</span>`;
                                } else if (typeof value === 'object') {
                                    value = `<code class="small">${JSON.stringify(value)}</code>`;
                                }
                                return `<td>${value}</td>`;
                            }).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    resultsDiv.innerHTML = tableHtml;
    
    // 更新统计信息
    document.getElementById('recordCount').textContent = formatNumber(data.length);
    document.getElementById('executionTime').textContent = executionTime + 'ms';
    document.getElementById('queryTime').textContent = new Date().toLocaleString();
    statsDiv.style.display = 'block';
    exportBtn.disabled = false;
}

// 添加到查询历史
function addToQueryHistory(sql, recordCount, executionTime) {
    const historyItem = {
        sql: sql,
        recordCount: recordCount,
        executionTime: executionTime,
        timestamp: new Date().toISOString()
    };
    
    // 添加到历史记录开头
    queryHistory.unshift(historyItem);
    
    // 只保留最近20条记录
    if (queryHistory.length > 20) {
        queryHistory = queryHistory.slice(0, 20);
    }
    
    // 保存到localStorage
    localStorage.setItem('queryHistory', JSON.stringify(queryHistory));
    
    // 更新显示
    displayQueryHistory();
}

// 显示查询历史
function displayQueryHistory() {
    const historyDiv = document.getElementById('queryHistory');
    
    if (queryHistory.length === 0) {
        historyDiv.innerHTML = `
            <div class="text-center text-muted py-3">
                <p>暂无查询历史</p>
            </div>
        `;
        return;
    }
    
    historyDiv.innerHTML = queryHistory.map((item, index) => `
        <div class="list-group-item list-group-item-action">
            <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">
                    <code class="small">${item.sql.length > 80 ? item.sql.substring(0, 80) + '...' : item.sql}</code>
                </h6>
                <small class="text-muted">${new Date(item.timestamp).toLocaleString()}</small>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    <i class="fas fa-list"></i> ${formatNumber(item.recordCount)} 条记录
                    <i class="fas fa-clock ms-2"></i> ${item.executionTime}ms
                </small>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="loadHistoryQuery(${index})">
                        <i class="fas fa-redo"></i>
                        重新执行
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// 加载历史查询
function loadHistoryQuery(index) {
    const historyItem = queryHistory[index];
    document.getElementById('sqlQuery').value = historyItem.sql;
    
    // 滚动到查询编辑器
    document.getElementById('sqlQuery').scrollIntoView({ behavior: 'smooth' });
}

// 清空查询
function clearQuery() {
    document.getElementById('sqlQuery').value = '';
    document.getElementById('queryTemplate').value = '';
}

// 导出查询结果
function exportResults() {
    if (!queryResults || queryResults.length === 0) {
        showAlert('warning', '没有结果可导出');
        return;
    }
    
    const blob = new Blob([JSON.stringify(queryResults, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showAlert('success', `成功导出 ${queryResults.length} 条记录`);
}

// 键盘快捷键
document.getElementById('sqlQuery').addEventListener('keydown', function(e) {
    // Ctrl+Enter 执行查询
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        executeQuery();
    }
});
</script>
{% endblock %}
