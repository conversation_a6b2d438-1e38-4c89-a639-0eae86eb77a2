# Yuan项目微服务架构

Yuan项目的微服务化实现，将各个模块拆分为独立的服务，通过API进行通信。

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────────────┐
│                    API Gateway (8000)                   │
│                   统一入口 + 路由                        │
└─────────────────────┬───────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌─────▼─────┐
│ Chat Service │ │Database │ │  Memory   │
│    (8002)    │ │Service  │ │ Service   │
│   AI对话服务  │ │ (8001) │ │  (8003)   │
└──────────────┘ └─────────┘ └───────────┘
```

## 📋 服务列表

| 服务名称 | 端口 | 功能描述 | 状态 |
|---------|------|----------|------|
| **API Gateway** | 8000 | 统一入口、路由转发、限流、认证 | ✅ 已实现 |
| **Database Service** | 8001 | 数据库操作、对话记录、日志存储 | ✅ 已实现 |
| **Chat Service** | 8002 | AI对话、会话管理、WebSocket | ✅ 已实现 |
| **Memory Service** | 8003 | 长期记忆、知识图谱 | 🚧 待实现 |
| **Tools Service** | 8004 | 工具集成、插件管理 | 🚧 待实现 |
| **Config Service** | 8005 | 配置管理、服务发现 | 🚧 待实现 |

## 🚀 快速开始

### 1. 安装依赖

```bash
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 安装微服务依赖
pip install fastapi uvicorn httpx
```

### 2. 启动所有服务

```bash
# 启动所有微服务
python microservices/start_services.py

# 或者单独启动
python microservices/start_services.py start
```

### 3. 验证服务状态

```bash
# 健康检查
curl http://127.0.0.1:8000/health

# 或访问API文档
# http://127.0.0.1:8000/docs
```

### 4. 测试微服务

```bash
# 运行集成测试
python microservices/client_example.py
```

## 🔧 服务详情

### API Gateway (8000)

**功能**：
- 统一API入口
- 请求路由转发
- 限流保护 (100请求/分钟)
- 请求日志记录
- 服务健康检查

**主要端点**：
- `GET /` - 网关信息
- `GET /health` - 健康检查
- `GET /services` - 服务列表
- `/api/{service}/{path}` - 代理转发

### Database Service (8001)

**功能**：
- 对话记录存储和查询
- 日志记录管理
- 系统监控数据
- 自定义SQL查询
- 数据维护操作

**主要端点**：
- `GET /status` - 数据库状态
- `POST /conversations` - 保存对话
- `GET /conversations/{session_id}` - 获取对话历史
- `POST /query` - 执行查询
- `POST /logs` - 保存日志
- `GET /logs` - 获取日志

### Chat Service (8002)

**功能**：
- AI对话处理
- 会话管理
- WebSocket实时通信
- 上下文维护
- 响应生成

**主要端点**：
- `POST /chat` - 发送消息
- `GET /sessions` - 活跃会话
- `GET /sessions/{session_id}/history` - 会话历史
- `WS /ws/{session_id}` - WebSocket连接

## 📊 优势分析

### ✅ 优势

1. **完全解耦**
   - 每个服务独立开发、部署、扩展
   - 技术栈自由选择
   - 故障隔离

2. **高可扩展性**
   - 水平扩展单个服务
   - 负载均衡
   - 弹性伸缩

3. **开发效率**
   - 团队并行开发
   - 独立版本控制
   - 快速迭代

4. **运维友好**
   - 独立监控
   - 精确故障定位
   - 灵活部署策略

### ⚠️ 挑战

1. **复杂性增加**
   - 服务间通信
   - 分布式事务
   - 数据一致性

2. **性能考虑**
   - 网络延迟
   - 序列化开销
   - 连接管理

3. **运维成本**
   - 多服务管理
   - 监控复杂度
   - 调试困难

## 🔄 通信模式

### 同步通信 (HTTP/REST)
```python
# 通过网关调用数据库服务
response = await client.post(
    "http://127.0.0.1:8000/api/database/conversations",
    json={"session_id": "xxx", "role": "user", "content": "hello"}
)
```

### 异步通信 (WebSocket)
```python
# 实时对话
async with websockets.connect("ws://127.0.0.1:8002/ws/session_001") as ws:
    await ws.send(json.dumps({"message": "Hello"}))
    response = await ws.recv()
```

## 📈 性能优化

### 1. 连接池
```python
# 使用连接池减少连接开销
async with httpx.AsyncClient() as client:
    # 复用连接
```

### 2. 缓存策略
```python
# Redis缓存热点数据
# 减少数据库查询
```

### 3. 负载均衡
```python
# 多实例部署
# nginx负载均衡
```

## 🛠️ 开发指南

### 添加新服务

1. **创建服务文件**
```python
# microservices/new_service.py
class NewService:
    def __init__(self):
        self.app = FastAPI(title="New Service")
```

2. **注册到网关**
```python
# 在gateway.py中添加服务
self.services["new_service"] = "http://127.0.0.1:8006"
```

3. **更新启动脚本**
```python
# 在start_services.py中添加服务配置
```

### 服务间调用

```python
async def call_other_service(self, service: str, endpoint: str, data: dict):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"http://127.0.0.1:8000/api/{service}/{endpoint}",
            json=data
        )
        return response.json()
```

## 🔍 监控和调试

### 健康检查
```bash
# 检查所有服务状态
curl http://127.0.0.1:8000/health
```

### 日志查看
```bash
# 查看服务日志
tail -f service.log
```

### API文档
- 网关: http://127.0.0.1:8000/docs
- 数据库服务: http://127.0.0.1:8001/docs
- 对话服务: http://127.0.0.1:8002/docs

## 🚦 部署建议

### 开发环境
- 本地启动所有服务
- 使用默认端口配置

### 生产环境
- Docker容器化部署
- Kubernetes编排
- 服务网格 (Istio)
- 监控系统 (Prometheus + Grafana)

## 📝 最佳实践

1. **API设计**
   - RESTful风格
   - 统一错误处理
   - 版本控制

2. **安全考虑**
   - API认证
   - 请求限流
   - 数据加密

3. **可观测性**
   - 结构化日志
   - 链路追踪
   - 性能监控

## 🎯 适用场景

### ✅ 适合微服务的场景
- 大型项目
- 多团队开发
- 高并发需求
- 复杂业务逻辑

### ❌ 不适合的场景
- 小型项目
- 单人开发
- 简单CRUD应用
- 对延迟敏感的应用

## 🔮 未来规划

1. **服务完善**
   - 实现Memory Service
   - 实现Tools Service
   - 实现Config Service

2. **功能增强**
   - 服务发现
   - 配置中心
   - 熔断器

3. **运维提升**
   - 容器化部署
   - 自动化运维
   - 监控告警

---

**总结**：微服务架构为Yuan项目提供了强大的扩展性和灵活性，但也带来了复杂性。建议根据项目规模和团队情况选择合适的架构模式。
