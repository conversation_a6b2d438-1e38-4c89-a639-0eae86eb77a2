#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务注册中心配置管理
"""

from pydantic_settings import BaseSettings


class ServiceRegistryConfig(BaseSettings):
    """服务注册中心配置"""
    
    service_name: str = "service_registry"
    service_port: int = 8500
    service_host: str = "127.0.0.1"
    
    # 健康检查配置
    heartbeat_timeout: int = 30      # 30秒无心跳则标记为不健康
    cleanup_timeout: int = 300       # 5分钟无心跳则移除服务
    health_check_interval: int = 10  # 健康检查间隔（秒）
    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        env_prefix = "REGISTRY_"


# 全局配置实例
config = ServiceRegistryConfig()
