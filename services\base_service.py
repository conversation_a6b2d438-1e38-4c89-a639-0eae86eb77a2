#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务基础类
提供服务注册、发现、心跳等通用功能
"""

import asyncio
import httpx
import time
import socket
from typing import Dict, List, Optional
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn


class BaseService:
    """微服务基础类"""
    
    def __init__(self, service_name: str, port: int, registry_url: str = "http://127.0.0.1:8500"):
        self.service_name = service_name
        self.port = port
        self.host = self.get_local_ip()
        self.registry_url = registry_url
        self.service_id: Optional[str] = None
        
        # 创建FastAPI应用
        self.app = FastAPI(
            title=f"Yuan {service_name.title()} Service",
            description=f"Yuan项目{service_name}微服务",
            version="1.0.0"
        )
        
        # 设置CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        self.setup_base_routes()
    
    def get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 连接到一个不存在的地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            return "127.0.0.1"
    
    def setup_base_routes(self):
        """设置基础路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "success": True,
                "data": {
                    "status": "healthy",
                    "service": self.service_name,
                    "service_id": self.service_id,
                    "host": self.host,
                    "port": self.port
                },
                "message": "服务运行正常",
                "timestamp": time.time()
            }
        
        @self.app.get("/info")
        async def service_info():
            """服务信息"""
            return {
                "success": True,
                "data": {
                    "service_name": self.service_name,
                    "service_id": self.service_id,
                    "host": self.host,
                    "port": self.port,
                    "registry_url": self.registry_url,
                    "health_check_url": f"http://{self.host}:{self.port}/health",
                    "version": "1.0.0",
                    "capabilities": ["服务注册", "服务发现", "心跳监控", "健康检查"]
                },
                "message": "获取服务信息成功",
                "timestamp": time.time()
            }
        
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print(f"🚀 {self.service_name} 服务启动中...")
            await self.register_to_registry()
            self.start_heartbeat()
            print(f"✅ {self.service_name} 服务启动完成")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """服务关闭事件"""
            print(f"🛑 {self.service_name} 服务关闭中...")
            await self.unregister_from_registry()
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
            print(f"✅ {self.service_name} 服务已关闭")
    
    async def register_to_registry(self):
        """注册到服务注册中心"""
        registration_data = {
            "service_name": self.service_name,
            "host": self.host,
            "port": self.port,
            "health_check_url": f"http://{self.host}:{self.port}/health",
            "metadata": {
                "version": "1.0.0",
                "started_at": time.time()
            }
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.registry_url}/register",
                    json=registration_data,
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        self.service_id = result["data"]["service_id"]
                        print(f"✅ 服务注册成功: {self.service_name} ({self.service_id[:8]})")
                    else:
                        print(f"❌ 服务注册失败: {result.get('message', '未知错误')}")
                else:
                    print(f"❌ 服务注册失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 连接注册中心失败: {e}")
    
    async def unregister_from_registry(self):
        """从服务注册中心注销"""
        if not self.service_id:
            return
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.registry_url}/unregister/{self.service_id}",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    print(f"✅ 服务注销成功: {self.service_name}")
                else:
                    print(f"⚠️ 服务注销失败: {response.text}")
                    
        except Exception as e:
            print(f"⚠️ 注销服务时出错: {e}")
    
    def start_heartbeat(self):
        """启动心跳任务"""
        async def heartbeat_loop():
            while True:
                try:
                    if self.service_id:
                        await self.send_heartbeat()
                    await asyncio.sleep(15)  # 每15秒发送一次心跳
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    print(f"⚠️ 心跳发送失败: {e}")
                    await asyncio.sleep(15)
        
        self.heartbeat_task = asyncio.create_task(heartbeat_loop())
    
    async def send_heartbeat(self):
        """发送心跳"""
        heartbeat_data = {
            "service_id": self.service_id,
            "status": "healthy",
            "metadata": {
                "last_heartbeat": time.time(),
                "uptime": time.time() - self.app.state.start_time if hasattr(self.app.state, 'start_time') else 0
            }
        }
        
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{self.registry_url}/heartbeat",
                    json=heartbeat_data,
                    timeout=5.0
                )
        except Exception as e:
            print(f"⚠️ 心跳发送失败: {e}")
    
    async def discover_service(self, service_name: str) -> List[Dict]:
        """发现其他服务"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.registry_url}/discover/{service_name}",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        return result["data"]["services"]
                    else:
                        print(f"⚠️ 服务发现失败: {result.get('message', '未知错误')}")
                        return []
                else:
                    print(f"⚠️ 服务发现失败: {response.text}")
                    return []
                    
        except Exception as e:
            print(f"⚠️ 服务发现出错: {e}")
            return []
    
    async def call_service(self, service_name: str, endpoint: str, method: str = "GET", 
                          data: Dict = None, params: Dict = None) -> Optional[Dict]:
        """调用其他服务"""
        services = await self.discover_service(service_name)
        
        if not services:
            print(f"⚠️ 未发现服务: {service_name}")
            return None
        
        # 简单的负载均衡：选择第一个健康的服务
        service = services[0]
        url = f"{service['url']}{endpoint}"
        
        try:
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, params=params or {}, timeout=10.0)
                elif method.upper() == "POST":
                    response = await client.post(url, json=data or {}, timeout=10.0)
                elif method.upper() == "PUT":
                    response = await client.put(url, json=data or {}, timeout=10.0)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, timeout=10.0)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                if response.status_code < 400:
                    return response.json()
                else:
                    print(f"⚠️ 服务调用失败: {service_name} - {response.status_code}")
                    return None
                    
        except Exception as e:
            print(f"⚠️ 调用服务 {service_name} 出错: {e}")
            return None
    
    def run(self, host: str = None, port: int = None):
        """运行服务"""
        run_host = host or self.host
        run_port = port or self.port
        
        print(f"🌟 启动 {self.service_name} 微服务")
        print(f"📍 服务地址: http://{run_host}:{run_port}")
        print(f"🏛️ 注册中心: {self.registry_url}")
        
        # 记录启动时间
        self.app.state.start_time = time.time()
        
        uvicorn.run(
            self.app,
            host=run_host,
            port=run_port,
            log_level="info"
        )
