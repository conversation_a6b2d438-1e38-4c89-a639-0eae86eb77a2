#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务客户端示例
演示如何通过API网关调用各个微服务
"""

import asyncio
import httpx
import json
import uuid


class YuanMicroserviceClient:
    """Yuan微服务客户端"""
    
    def __init__(self, gateway_url: str = "http://127.0.0.1:8000"):
        self.gateway_url = gateway_url
        self.session_id = str(uuid.uuid4())
    
    async def check_health(self):
        """检查服务健康状态"""
        print("🔍 检查微服务健康状态...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.gateway_url}/health")
                health_data = response.json()
                
                print(f"网关状态: {health_data['gateway']}")
                print("服务状态:")
                for service, status in health_data['services'].items():
                    status_icon = "✅" if status['status'] == 'healthy' else "❌"
                    print(f"  {status_icon} {service}: {status['status']}")
                
                return health_data
                
            except Exception as e:
                print(f"❌ 健康检查失败: {e}")
                return None
    
    async def test_database_service(self):
        """测试数据库服务"""
        print("\n🗄️ 测试数据库服务...")
        
        async with httpx.AsyncClient() as client:
            try:
                # 获取数据库状态
                response = await client.get(f"{self.gateway_url}/api/database/status")
                status = response.json()
                print(f"数据库文件: {status['database_file']}")
                print(f"文件大小: {status['file_size_mb']:.2f} MB")
                print(f"总记录数: {status['total_records']}")
                
                # 保存测试对话
                conversation_data = {
                    "session_id": self.session_id,
                    "role": "user",
                    "content": "这是通过微服务API保存的测试消息",
                    "metadata": {"client": "microservice_test", "api_version": "1.0"}
                }
                
                response = await client.post(
                    f"{self.gateway_url}/api/database/conversations",
                    json=conversation_data
                )
                
                if response.status_code == 200:
                    print("✅ 对话记录保存成功")
                else:
                    print(f"❌ 对话记录保存失败: {response.text}")
                
                return True
                
            except Exception as e:
                print(f"❌ 数据库服务测试失败: {e}")
                return False
    
    async def test_chat_service(self):
        """测试AI对话服务"""
        print("\n🤖 测试AI对话服务...")
        
        async with httpx.AsyncClient() as client:
            try:
                # 发送对话请求
                chat_data = {
                    "session_id": self.session_id,
                    "message": "你好，我正在测试Yuan项目的微服务架构",
                    "model": "gpt-4",
                    "temperature": 0.7
                }
                
                response = await client.post(
                    f"{self.gateway_url}/api/chat",
                    json=chat_data
                )
                
                if response.status_code == 200:
                    chat_response = response.json()
                    print("✅ AI对话成功")
                    print(f"用户消息: {chat_response['message']}")
                    print(f"AI回复: {chat_response['response']}")
                    print(f"会话ID: {chat_response['session_id']}")
                else:
                    print(f"❌ AI对话失败: {response.text}")
                    return False
                
                # 获取对话历史
                response = await client.get(
                    f"{self.gateway_url}/api/chat/sessions/{self.session_id}/history"
                )
                
                if response.status_code == 200:
                    history = response.json()
                    print(f"✅ 获取对话历史成功，共 {history['count']} 条记录")
                else:
                    print(f"❌ 获取对话历史失败: {response.text}")
                
                return True
                
            except Exception as e:
                print(f"❌ AI对话服务测试失败: {e}")
                return False
    
    async def test_database_query(self):
        """测试数据库查询功能"""
        print("\n📊 测试数据库查询功能...")
        
        async with httpx.AsyncClient() as client:
            try:
                # 执行统计查询
                query_data = {
                    "sql": "SELECT role, COUNT(*) as count FROM conversations GROUP BY role"
                }
                
                response = await client.post(
                    f"{self.gateway_url}/api/database/query",
                    json=query_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ 数据库查询成功")
                    print("角色统计:")
                    for row in result['data']:
                        print(f"  {row['role']}: {row['count']} 条")
                else:
                    print(f"❌ 数据库查询失败: {response.text}")
                    return False
                
                return True
                
            except Exception as e:
                print(f"❌ 数据库查询测试失败: {e}")
                return False
    
    async def test_conversation_flow(self):
        """测试完整的对话流程"""
        print("\n💬 测试完整对话流程...")
        
        messages = [
            "你好，我是新用户",
            "请介绍一下Yuan项目",
            "微服务架构有什么优势？",
            "谢谢你的回答"
        ]
        
        async with httpx.AsyncClient() as client:
            try:
                for i, message in enumerate(messages, 1):
                    print(f"\n第 {i} 轮对话:")
                    print(f"用户: {message}")
                    
                    chat_data = {
                        "session_id": self.session_id,
                        "message": message,
                        "model": "gpt-4"
                    }
                    
                    response = await client.post(
                        f"{self.gateway_url}/api/chat",
                        json=chat_data
                    )
                    
                    if response.status_code == 200:
                        chat_response = response.json()
                        print(f"AI: {chat_response['response']}")
                    else:
                        print(f"❌ 对话失败: {response.text}")
                        return False
                    
                    # 短暂延迟
                    await asyncio.sleep(1)
                
                print("\n✅ 完整对话流程测试成功")
                return True
                
            except Exception as e:
                print(f"❌ 对话流程测试失败: {e}")
                return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 Yuan微服务集成测试")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.check_health),
            ("数据库服务", self.test_database_service),
            ("AI对话服务", self.test_chat_service),
            ("数据库查询", self.test_database_query),
            ("完整对话流程", self.test_conversation_flow)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                results[test_name] = False
        
        # 测试结果汇总
        print("\n" + "=" * 50)
        print("📋 测试结果汇总:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过！微服务架构运行正常")
        else:
            print("⚠️ 部分测试失败，请检查服务状态")


async def main():
    """主函数"""
    client = YuanMicroserviceClient()
    await client.run_all_tests()


if __name__ == "__main__":
    print("🚀 启动Yuan微服务客户端测试")
    print("请确保微服务已启动 (python start_services.py)")
    print()
    
    asyncio.run(main())
