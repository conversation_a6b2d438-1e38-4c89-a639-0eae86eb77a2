# 数据库模块

## 模块功能描述
数据库模块提供统一的数据库操作接口，支持对话记录、系统监控、日志记录等数据的存储和查询。采用异步操作和批量写入优化，确保高性能和数据可靠性。

## 主要特性列表
- ✅ 异步数据库操作
- ✅ 批量写入优化
- ✅ 对话记录存储和查询
- ✅ 系统监控数据存储
- ✅ 日志记录存储
- ✅ 记忆数据存储（预留）
- ✅ 自定义查询支持
- ✅ 数据清理和维护
- ✅ 手动操作工具

## 使用场景说明
- AI对话记录的持久化存储
- 系统监控数据的实时写入
- 应用日志的集中存储
- 数据查询和分析
- 数据库维护和管理

## 依赖关系说明
- **aiosqlite**: 异步SQLite数据库驱动
- **sqlalchemy**: ORM框架和数据库抽象层
- **alembic**: 数据库迁移工具

## 数据表设计

### conversations - 对话记录表（已优化）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键，自增 |
| session_id | String(255) | 会话ID，用于逻辑分组和主题区分 |
| role | String(50) | 角色：user/assistant/system |
| content | Text | 对话内容 |
| timestamp | DateTime | 对话发生时间（核心时间字段） |
| attachments | JSON | 附件信息数组：[{"path": "/uploads/file.jpg", "size": 1024, "name": "image.jpg", "mime_type": "image/jpeg"}] |
| meta_data | JSON | 元数据：模型参数、token数、处理时间等 |
| created_at | DateTime | 记录创建时间（用于审计） |

**优化说明**：
- ✅ **新增** `attachments` 字段支持图片、文件等附件
- ✅ **移除** `updated_at` 字段 - 对话记录应该是不可变的
- ✅ **优化** 时间字段用途 - `timestamp`记录对话时间，`created_at`记录数据库写入时间
- ✅ **明确** `session_id` 用途 - 用于逻辑分组，即使不清除上下文也很有价值

### system_metrics - 系统监控数据表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键，自增 |
| timestamp | DateTime | 采集时间，索引 |
| cpu_percent | Float | CPU使用率(%) |
| memory_percent | Float | 内存使用率(%) |
| memory_used | Float | 已用内存(MB) |
| memory_total | Float | 总内存(MB) |
| disk_percent | Float | 磁盘使用率(%) |
| network_sent | Float | 网络发送(MB) |
| network_recv | Float | 网络接收(MB) |
| additional_data | JSON | 额外监控数据 |

### logs - 日志记录表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键，自增 |
| timestamp | DateTime | 日志时间，索引 |
| level | String(20) | 日志级别，索引 |
| module | String(100) | 模块名称，索引 |
| message | Text | 日志消息 |
| function_name | String(100) | 函数名称 |
| line_number | Integer | 行号 |
| exception_info | Text | 异常信息 |
| extra_data | JSON | 额外数据 |

### memories - 记忆数据表（预留）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键，自增 |
| content | Text | 记忆内容 |
| memory_type | String(50) | 记忆类型，索引 |
| importance | Float | 重要性评分(0-10) |
| source_type | String(50) | 来源类型 |
| tags | JSON | 标签列表 |
| embedding | JSON | 向量嵌入（预留） |

## 文件结构
```
database/
├── __init__.py          # 模块初始化
├── core.py              # 数据库核心类
├── models.py            # 数据模型定义
├── db_manager.py        # 手动操作数据库程序
├── README.md            # 功能文档（本文件）
└── API.md               # API接口文档
```

## 快速开始

### 基本使用
```python
import asyncio
from database import db_core

async def example():
    # 初始化数据库
    await db_core.initialize()
    
    # 保存对话记录
    await db_core.save_conversation(
        session_id="session_001",
        role="user", 
        content="你好",
        metadata={"model": "gpt-4o", "temperature": 0.3}
    )
    
    # 获取对话历史
    history = await db_core.get_conversation_history("session_001")
    print(history)
    
    # 关闭数据库
    await db_core.close()

asyncio.run(example())
```

### 手动操作数据库
```bash
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 运行数据库管理器
python database/db_manager.py
```

## 性能优化特性

### 批量写入
- 自动批量处理写入操作
- 可配置批次大小和超时时间
- 减少数据库I/O操作

### 异步操作
- 全异步数据库操作
- 非阻塞的数据写入
- 支持高并发访问

### 连接池管理
- 自动管理数据库连接
- 连接复用和回收
- 防止连接泄漏

## 功能清单
| 功能 | 状态 | 描述 |
|------|------|------|
| 数据库初始化 | ✅ 已实现 | 自动创建表结构 |
| 对话记录存储 | ✅ 已实现 | 异步保存对话数据 |
| 对话历史查询 | ✅ 已实现 | 按会话ID查询历史 |
| 系统监控存储 | ✅ 已实现 | 高频监控数据写入 |
| 日志记录存储 | ✅ 已实现 | 应用日志集中存储 |
| 自定义查询 | ✅ 已实现 | 支持SQL查询 |
| 数据清理 | ✅ 已实现 | 自动清理旧数据 |
| 批量写入优化 | ✅ 已实现 | 提高写入性能 |
| 手动管理工具 | ✅ 已实现 | 命令行管理界面 |
| 数据导出导入 | ✅ 已实现 | JSON格式数据交换 |

## 配置说明

### 数据库路径
默认数据库文件：`data/yuan.db`

### 批量写入配置
- 批次大小：100条记录
- 超时时间：5秒
- 可在DatabaseCore初始化时调整

### 数据保留策略
- 系统监控数据：默认保留30天
- 日志数据：默认保留30天
- 对话记录：永久保留

## 更新日志
- 2025-06-13: 初始版本，实现基础数据库功能
