#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API网关服务
统一入口，路由转发，认证授权，限流等
"""

import asyncio
import time
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import httpx
import uvicorn
from collections import defaultdict


class RateLimiter:
    """简单的限流器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(list)
    
    def is_allowed(self, client_id: str) -> bool:
        """检查是否允许请求"""
        now = time.time()
        window_start = now - self.window_seconds
        
        # 清理过期请求
        self.requests[client_id] = [
            req_time for req_time in self.requests[client_id]
            if req_time > window_start
        ]
        
        # 检查是否超过限制
        if len(self.requests[client_id]) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests[client_id].append(now)
        return True


class APIGateway:
    """API网关"""
    
    def __init__(self):
        self.app = FastAPI(
            title="Yuan API Gateway",
            description="Yuan项目API网关",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 服务注册表
        self.services = {
            "database": "http://127.0.0.1:8001",
            "chat": "http://127.0.0.1:8002",
            "memory": "http://127.0.0.1:8003",
            "tools": "http://127.0.0.1:8004",
            "config": "http://127.0.0.1:8005",
        }
        
        # 限流器
        self.rate_limiter = RateLimiter(max_requests=100, window_seconds=60)
        
        self.setup_middleware()
        self.setup_routes()
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        @self.app.middleware("http")
        async def rate_limit_middleware(request: Request, call_next):
            """限流中间件"""
            client_ip = request.client.host
            
            if not self.rate_limiter.is_allowed(client_ip):
                return JSONResponse(
                    status_code=429,
                    content={"error": "请求过于频繁，请稍后再试"}
                )
            
            response = await call_next(request)
            return response
        
        @self.app.middleware("http")
        async def logging_middleware(request: Request, call_next):
            """日志中间件"""
            start_time = time.time()
            
            response = await call_next(request)
            
            process_time = time.time() - start_time
            print(f"📝 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
            
            return response
    
    async def proxy_request(self, service_name: str, path: str, method: str, 
                           request_data: Dict = None, params: Dict = None) -> Dict:
        """代理请求到后端服务"""
        if service_name not in self.services:
            raise HTTPException(status_code=404, detail=f"服务 {service_name} 不存在")
        
        service_url = self.services[service_name]
        url = f"{service_url}{path}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, params=params or {})
                elif method.upper() == "POST":
                    response = await client.post(url, json=request_data or {})
                elif method.upper() == "PUT":
                    response = await client.put(url, json=request_data or {})
                elif method.upper() == "DELETE":
                    response = await client.delete(url)
                else:
                    raise HTTPException(status_code=405, detail=f"不支持的HTTP方法: {method}")
                
                # 检查响应状态
                if response.status_code >= 400:
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"后端服务错误: {response.text}"
                    )
                
                return response.json()
                
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=503,
                    detail=f"服务 {service_name} 不可用: {str(e)}"
                )
            except httpx.TimeoutException:
                raise HTTPException(
                    status_code=504,
                    detail=f"服务 {service_name} 响应超时"
                )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/")
        async def root():
            """根路径"""
            return {
                "service": "Yuan API Gateway",
                "version": "1.0.0",
                "status": "running",
                "services": list(self.services.keys())
            }
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            service_status = {}
            
            for service_name, service_url in self.services.items():
                try:
                    async with httpx.AsyncClient(timeout=5.0) as client:
                        response = await client.get(f"{service_url}/health")
                        service_status[service_name] = {
                            "status": "healthy" if response.status_code == 200 else "unhealthy",
                            "url": service_url
                        }
                except Exception:
                    service_status[service_name] = {
                        "status": "unreachable",
                        "url": service_url
                    }
            
            return {
                "gateway": "healthy",
                "services": service_status
            }
        
        @self.app.get("/services")
        async def list_services():
            """列出所有服务"""
            return {
                "services": self.services,
                "count": len(self.services)
            }
        
        # 数据库服务路由
        @self.app.get("/api/database/status")
        async def database_status():
            return await self.proxy_request("database", "/status", "GET")
        
        @self.app.post("/api/database/conversations")
        async def save_conversation(request: Request):
            data = await request.json()
            return await self.proxy_request("database", "/conversations", "POST", data)
        
        @self.app.get("/api/database/conversations/{session_id}")
        async def get_conversations(session_id: str, limit: int = 50):
            return await self.proxy_request(
                "database", f"/conversations/{session_id}", "GET", 
                params={"limit": limit}
            )
        
        @self.app.post("/api/database/query")
        async def execute_query(request: Request):
            data = await request.json()
            return await self.proxy_request("database", "/query", "POST", data)
        
        # AI对话服务路由
        @self.app.post("/api/chat")
        async def chat(request: Request):
            data = await request.json()
            return await self.proxy_request("chat", "/chat", "POST", data)
        
        @self.app.get("/api/chat/sessions")
        async def get_chat_sessions():
            return await self.proxy_request("chat", "/sessions", "GET")
        
        @self.app.get("/api/chat/sessions/{session_id}/history")
        async def get_chat_history(session_id: str, limit: int = 50):
            return await self.proxy_request(
                "chat", f"/sessions/{session_id}/history", "GET",
                params={"limit": limit}
            )
        
        # 通用代理路由（用于其他服务）
        @self.app.api_route("/api/{service_name}/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
        async def generic_proxy(service_name: str, path: str, request: Request):
            """通用代理路由"""
            method = request.method
            
            # 获取请求数据
            request_data = None
            if method in ["POST", "PUT"]:
                try:
                    request_data = await request.json()
                except Exception:
                    request_data = {}
            
            # 获取查询参数
            params = dict(request.query_params)
            
            return await self.proxy_request(
                service_name, f"/{path}", method, request_data, params
            )
        
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print("🚀 API网关启动中...")
            print(f"📋 注册的服务: {list(self.services.keys())}")
            print("✅ API网关启动完成")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """服务关闭事件"""
            print("🛑 API网关关闭中...")
            print("✅ API网关已关闭")
    
    def run(self, host: str = "127.0.0.1", port: int = 8000):
        """运行网关"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )


# 网关实例
gateway = APIGateway()

if __name__ == "__main__":
    print("🌐 启动Yuan API网关")
    print("📍 网关地址: http://127.0.0.1:8000")
    print("📚 API文档: http://127.0.0.1:8000/docs")
    print("🔍 健康检查: http://127.0.0.1:8000/health")
    
    gateway.run()
