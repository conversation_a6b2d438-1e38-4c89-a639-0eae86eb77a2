#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BaseService的功能
"""

from services.base_service import BaseService


class TestService(BaseService):
    """测试服务"""
    
    def __init__(self):
        super().__init__("test_service", 8999)
        self.setup_test_routes()
    
    def setup_test_routes(self):
        """设置测试路由"""
        
        @self.app.get("/test")
        async def test_endpoint():
            """测试端点"""
            return {
                "success": True,
                "data": {
                    "message": "这是一个测试端点",
                    "service": self.service_name
                },
                "message": "测试成功",
                "timestamp": __import__('time').time()
            }


if __name__ == "__main__":
    service = TestService()
    service.run()
