#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库微服务
提供RESTful API接口访问数据库功能
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Optional, Any
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from database.core import DatabaseCore


# 数据模型定义
class ConversationRequest(BaseModel):
    session_id: str
    role: str
    content: str
    attachments: Optional[List[Dict]] = None
    metadata: Optional[Dict] = None


class ConversationResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None


class QueryRequest(BaseModel):
    sql: str


class StatusResponse(BaseModel):
    database_file: str
    file_size_mb: float
    table_stats: Dict[str, int]
    total_records: int
    service_info: Dict[str, Any]


# 数据库服务类
class DatabaseService:
    """数据库微服务"""
    
    def __init__(self):
        self.app = FastAPI(
            title="Yuan Database Service",
            description="Yuan项目数据库微服务",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        self.db: Optional[DatabaseCore] = None
        self.setup_middleware()
        self.setup_routes()
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    async def get_db(self) -> DatabaseCore:
        """获取数据库实例"""
        if self.db is None:
            self.db = DatabaseCore()
            await self.db.initialize()
        return self.db
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "service": "database"}
        
        @self.app.get("/status", response_model=StatusResponse)
        async def get_status(db: DatabaseCore = Depends(self.get_db)):
            """获取数据库状态"""
            try:
                # 数据库文件信息
                db_path = db.db_path
                file_info = {
                    "path": str(db_path),
                    "exists": db_path.exists(),
                    "size_mb": db_path.stat().st_size / (1024 * 1024) if db_path.exists() else 0
                }
                
                # 表统计信息
                stats = await db.get_table_stats()
                
                return StatusResponse(
                    database_file=str(db_path),
                    file_size_mb=file_info["size_mb"],
                    table_stats=stats,
                    total_records=sum(stats.values()),
                    service_info={
                        "service": "database",
                        "version": "1.0.0",
                        "port": 8001
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")
        
        @self.app.post("/conversations", response_model=ConversationResponse)
        async def save_conversation(
            request: ConversationRequest,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """保存对话记录"""
            try:
                await db.save_conversation(
                    session_id=request.session_id,
                    role=request.role,
                    content=request.content,
                    attachments=request.attachments,
                    metadata=request.metadata
                )
                
                return ConversationResponse(
                    success=True,
                    message="对话记录保存成功",
                    data={
                        "session_id": request.session_id,
                        "role": request.role
                    }
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"保存对话失败: {str(e)}")
        
        @self.app.get("/conversations/{session_id}")
        async def get_conversation_history(
            session_id: str,
            limit: int = 50,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """获取对话历史"""
            try:
                history = await db.get_conversation_history(session_id, limit)
                return {
                    "success": True,
                    "data": history,
                    "count": len(history),
                    "session_id": session_id
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")
        
        @self.app.post("/query")
        async def execute_query(
            request: QueryRequest,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """执行自定义查询"""
            try:
                # 安全检查：只允许SELECT查询
                if not request.sql.strip().upper().startswith('SELECT'):
                    raise HTTPException(status_code=400, detail="只支持SELECT查询")
                
                result = await db.execute_query(request.sql)
                return {
                    "success": True,
                    "data": result,
                    "count": len(result)
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"查询执行失败: {str(e)}")
        
        @self.app.post("/logs")
        async def save_log(
            level: str,
            module: str,
            message: str,
            function_name: Optional[str] = None,
            line_number: Optional[int] = None,
            exception_info: Optional[str] = None,
            extra_data: Optional[Dict] = None,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """保存日志记录"""
            try:
                await db.save_log(
                    level=level,
                    module=module,
                    message=message,
                    function_name=function_name,
                    line_number=line_number,
                    exception_info=exception_info,
                    extra_data=extra_data
                )
                
                return {
                    "success": True,
                    "message": "日志记录保存成功"
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"保存日志失败: {str(e)}")
        
        @self.app.get("/logs")
        async def get_logs(
            level: Optional[str] = None,
            hours: int = 24,
            limit: int = 100,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """获取日志记录"""
            try:
                logs = await db.get_logs(level=level, hours=hours, limit=limit)
                return {
                    "success": True,
                    "data": logs,
                    "count": len(logs)
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")
        
        @self.app.post("/maintenance/cleanup")
        async def cleanup_old_data(
            days: int = 30,
            db: DatabaseCore = Depends(self.get_db)
        ):
            """清理旧数据"""
            try:
                await db.cleanup_old_data(days)
                return {
                    "success": True,
                    "message": f"已清理 {days} 天前的旧数据"
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"清理数据失败: {str(e)}")
        
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print("🚀 数据库微服务启动中...")
            await self.get_db()  # 初始化数据库
            print("✅ 数据库微服务启动完成")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """服务关闭事件"""
            print("🛑 数据库微服务关闭中...")
            if self.db:
                await self.db.close()
            print("✅ 数据库微服务已关闭")
    
    def run(self, host: str = "127.0.0.1", port: int = 8001):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )


# 服务实例
service = DatabaseService()

if __name__ == "__main__":
    print("🗄️ 启动Yuan数据库微服务")
    print("📍 服务地址: http://127.0.0.1:8001")
    print("📚 API文档: http://127.0.0.1:8001/docs")
    
    service.run()
