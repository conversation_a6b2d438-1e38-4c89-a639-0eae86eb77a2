#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库核心模块
提供统一的数据库操作接口
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import aiosqlite
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker

from .models import Base, Conversation, SystemMetrics, LogEntry, Memory, DatabaseConfig


class DatabaseCore:
    """数据库核心类"""
    
    def __init__(self, db_path: str = "data/yuan.db"):
        """
        初始化数据库核心
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 异步引擎和会话
        self.async_engine = create_async_engine(
            f"sqlite+aiosqlite:///{self.db_path}",
            echo=False,  # 设为True可以看到SQL语句
            pool_pre_ping=True
        )
        self.async_session_maker = async_sessionmaker(
            self.async_engine, 
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 同步引擎（用于初始化和手动操作）
        self.sync_engine = create_engine(f"sqlite:///{self.db_path}")
        self.sync_session_maker = sessionmaker(bind=self.sync_engine)
        
        # 写入队列（用于批量写入优化）
        self.write_queue = asyncio.Queue()
        self.batch_size = 100
        self.batch_timeout = 5.0  # 秒
        self._batch_writer_task = None
        self._is_running = False
    
    async def initialize(self):
        """初始化数据库"""
        # 创建所有表
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # 启动批量写入任务
        await self.start_batch_writer()
        
        print(f"数据库初始化完成: {self.db_path}")
    
    async def start_batch_writer(self):
        """启动批量写入任务"""
        if not self._is_running:
            self._is_running = True
            self._batch_writer_task = asyncio.create_task(self._batch_writer())
    
    async def stop_batch_writer(self):
        """停止批量写入任务"""
        self._is_running = False
        if self._batch_writer_task:
            self._batch_writer_task.cancel()
            try:
                await self._batch_writer_task
            except asyncio.CancelledError:
                pass
    
    async def _batch_writer(self):
        """批量写入任务"""
        batch = []
        last_write_time = datetime.now()
        
        while self._is_running:
            try:
                # 等待新数据或超时
                timeout = max(0.1, self.batch_timeout - (datetime.now() - last_write_time).total_seconds())
                item = await asyncio.wait_for(self.write_queue.get(), timeout=timeout)
                batch.append(item)
                
                # 检查是否需要写入
                should_write = (
                    len(batch) >= self.batch_size or
                    (datetime.now() - last_write_time).total_seconds() >= self.batch_timeout
                )
                
                if should_write and batch:
                    await self._write_batch(batch)
                    batch.clear()
                    last_write_time = datetime.now()
                    
            except asyncio.TimeoutError:
                # 超时，写入当前批次
                if batch:
                    await self._write_batch(batch)
                    batch.clear()
                    last_write_time = datetime.now()
            except Exception as e:
                print(f"批量写入错误: {e}")
                await asyncio.sleep(1)
    
    async def _write_batch(self, batch: List[Dict[str, Any]]):
        """写入批次数据"""
        if not batch:
            return
        
        try:
            async with self.async_session_maker() as session:
                for item in batch:
                    model_class = item['model_class']
                    data = item['data']
                    obj = model_class(**data)
                    session.add(obj)
                
                await session.commit()
                
        except Exception as e:
            print(f"批量写入数据库失败: {e}")
    
    # ==================== 对话相关方法 ====================
    
    async def save_conversation(self, session_id: str, role: str, content: str,
                              attachments: Optional[List[Dict]] = None,
                              metadata: Optional[Dict] = None):
        """
        保存对话记录

        Args:
            session_id: 会话ID
            role: 角色（user/assistant/system）
            content: 对话内容
            attachments: 附件列表，格式：[{"path": "/uploads/file.jpg", "size": 1024, "name": "image.jpg", "mime_type": "image/jpeg"}]
            metadata: 元数据
        """
        data = {
            'session_id': session_id,
            'role': role,
            'content': content,
            'attachments': attachments,
            'meta_data': metadata
        }

        await self.write_queue.put({
            'model_class': Conversation,
            'data': data
        })
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict]:
        """
        获取对话历史
        
        Args:
            session_id: 会话ID
            limit: 限制数量
            
        Returns:
            对话历史列表
        """
        async with self.async_session_maker() as session:
            result = await session.execute(
                text("""
                SELECT role, content, timestamp, attachments, meta_data
                FROM conversations
                WHERE session_id = :session_id
                ORDER BY timestamp DESC
                LIMIT :limit
                """),
                {'session_id': session_id, 'limit': limit}
            )

            conversations = []
            for row in result:
                # 处理timestamp，可能是字符串或datetime对象
                timestamp_str = row.timestamp
                if hasattr(row.timestamp, 'isoformat'):
                    timestamp_str = row.timestamp.isoformat()

                conversations.append({
                    'role': row.role,
                    'content': row.content,
                    'timestamp': timestamp_str,
                    'attachments': json.loads(row.attachments) if row.attachments else None,
                    'metadata': json.loads(row.meta_data) if row.meta_data else None
                })
            
            return list(reversed(conversations))  # 按时间正序返回
    
    # ==================== 系统监控相关方法 ====================
    
    async def save_system_metrics(self, metrics: Dict[str, Any]):
        """
        保存系统监控数据
        
        Args:
            metrics: 系统指标数据
        """
        await self.write_queue.put({
            'model_class': SystemMetrics,
            'data': metrics
        })
    
    async def get_system_metrics(self, hours: int = 24) -> List[Dict]:
        """
        获取系统监控数据
        
        Args:
            hours: 获取最近几小时的数据
            
        Returns:
            系统监控数据列表
        """
        async with self.async_session_maker() as session:
            result = await session.execute(
                text("""
                SELECT * FROM system_metrics 
                WHERE timestamp >= datetime('now', '-{} hours')
                ORDER BY timestamp DESC
                """.format(hours))
            )
            
            metrics = []
            for row in result:
                # 处理timestamp，可能是字符串或datetime对象
                timestamp_str = row.timestamp
                if hasattr(row.timestamp, 'isoformat'):
                    timestamp_str = row.timestamp.isoformat()

                metrics.append({
                    'timestamp': timestamp_str,
                    'cpu_percent': row.cpu_percent,
                    'memory_percent': row.memory_percent,
                    'memory_used': row.memory_used,
                    'memory_total': row.memory_total,
                    'disk_percent': row.disk_percent,
                    'network_sent': row.network_sent,
                    'network_recv': row.network_recv,
                    'additional_data': json.loads(row.additional_data) if row.additional_data else None
                })
            
            return metrics
    
    # ==================== 日志相关方法 ====================
    
    async def save_log(self, level: str, module: str, message: str, 
                      function_name: Optional[str] = None, line_number: Optional[int] = None,
                      exception_info: Optional[str] = None, extra_data: Optional[Dict] = None):
        """
        保存日志记录
        
        Args:
            level: 日志级别
            module: 模块名称
            message: 日志消息
            function_name: 函数名称
            line_number: 行号
            exception_info: 异常信息
            extra_data: 额外数据
        """
        data = {
            'level': level,
            'module': module,
            'message': message,
            'function_name': function_name,
            'line_number': line_number,
            'exception_info': exception_info,
            'extra_data': extra_data
        }
        
        await self.write_queue.put({
            'model_class': LogEntry,
            'data': data
        })
    
    async def get_logs(self, level: Optional[str] = None, module: Optional[str] = None, 
                      hours: int = 24, limit: int = 1000) -> List[Dict]:
        """
        获取日志记录
        
        Args:
            level: 日志级别过滤
            module: 模块名称过滤
            hours: 获取最近几小时的日志
            limit: 限制数量
            
        Returns:
            日志记录列表
        """
        conditions = ["timestamp >= datetime('now', '-{} hours')".format(hours)]
        params = {'limit': limit}
        
        if level:
            conditions.append("level = :level")
            params['level'] = level
        
        if module:
            conditions.append("module = :module")
            params['module'] = module
        
        where_clause = " AND ".join(conditions)
        
        async with self.async_session_maker() as session:
            result = await session.execute(
                text(f"""
                SELECT * FROM logs 
                WHERE {where_clause}
                ORDER BY timestamp DESC 
                LIMIT :limit
                """),
                params
            )
            
            logs = []
            for row in result:
                # 处理timestamp，可能是字符串或datetime对象
                timestamp_str = row.timestamp
                if hasattr(row.timestamp, 'isoformat'):
                    timestamp_str = row.timestamp.isoformat()

                logs.append({
                    'timestamp': timestamp_str,
                    'level': row.level,
                    'module': row.module,
                    'message': row.message,
                    'function_name': row.function_name,
                    'line_number': row.line_number,
                    'exception_info': row.exception_info,
                    'extra_data': json.loads(row.extra_data) if row.extra_data else None
                })
            
            return logs
    
    # ==================== 通用方法 ====================
    
    async def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """
        执行自定义查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        async with self.async_session_maker() as session:
            result = await session.execute(text(query), params or {})
            
            # 转换为字典列表
            columns = result.keys()
            rows = []
            for row in result:
                rows.append(dict(zip(columns, row)))
            
            return rows
    
    async def get_table_stats(self) -> Dict[str, int]:
        """
        获取各表的记录数统计
        
        Returns:
            表名和记录数的字典
        """
        stats = {}
        tables = ['conversations', 'system_metrics', 'logs', 'memories', 'config']
        
        async with self.async_session_maker() as session:
            for table in tables:
                result = await session.execute(text(f"SELECT COUNT(*) as count FROM {table}"))
                count = result.scalar()
                stats[table] = count
        
        return stats
    
    async def cleanup_old_data(self, days: int = 30):
        """
        清理旧数据
        
        Args:
            days: 保留最近几天的数据
        """
        async with self.async_session_maker() as session:
            # 清理旧的系统监控数据
            await session.execute(
                text("DELETE FROM system_metrics WHERE timestamp < datetime('now', '-{} days')".format(days))
            )
            
            # 清理旧的日志数据
            await session.execute(
                text("DELETE FROM logs WHERE timestamp < datetime('now', '-{} days')".format(days))
            )
            
            await session.commit()
            print(f"已清理 {days} 天前的旧数据")
    
    async def close(self):
        """关闭数据库连接"""
        await self.stop_batch_writer()
        await self.async_engine.dispose()
        self.sync_engine.dispose()


# 全局数据库实例
db_core = DatabaseCore()
