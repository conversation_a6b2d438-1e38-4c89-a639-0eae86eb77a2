#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话微服务
提供AI对话功能的RESTful API接口
"""

import asyncio
import sys
import httpx
from pathlib import Path
from typing import List, Dict, Optional, Any
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import uuid

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


# 数据模型定义
class ChatRequest(BaseModel):
    session_id: Optional[str] = None
    message: str
    attachments: Optional[List[Dict]] = None
    model: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: Optional[int] = None


class ChatResponse(BaseModel):
    success: bool
    session_id: str
    message: str
    response: str
    metadata: Dict[str, Any]


class SessionInfo(BaseModel):
    session_id: str
    message_count: int
    created_at: str
    last_activity: str


# AI对话服务类
class ChatService:
    """AI对话微服务"""
    
    def __init__(self):
        self.app = FastAPI(
            title="Yuan Chat Service",
            description="Yuan项目AI对话微服务",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        self.database_service_url = "http://127.0.0.1:8001"
        self.active_sessions = {}  # 活跃会话管理
        self.setup_middleware()
        self.setup_routes()
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    async def call_database_service(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """调用数据库服务"""
        url = f"{self.database_service_url}{endpoint}"
        
        async with httpx.AsyncClient() as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(url, params=data or {})
                elif method.upper() == "POST":
                    response = await client.post(url, json=data or {})
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                response.raise_for_status()
                return response.json()
            
            except httpx.RequestError as e:
                raise HTTPException(status_code=503, detail=f"数据库服务不可用: {str(e)}")
            except httpx.HTTPStatusError as e:
                raise HTTPException(status_code=e.response.status_code, detail=f"数据库服务错误: {e.response.text}")
    
    async def simulate_ai_response(self, message: str, session_id: str, model: str = "gpt-4") -> str:
        """模拟AI响应（实际项目中这里会调用真实的AI API）"""
        # 这里是模拟响应，实际项目中会调用OpenAI API或其他AI服务
        responses = [
            f"我理解您说的'{message}'。作为Yuan项目的AI助手，我很乐意为您提供帮助。",
            f"关于'{message}'这个问题，让我为您详细解答...",
            f"您提到的'{message}'很有趣。基于我的理解，我认为...",
            f"感谢您的问题'{message}'。根据我的知识库，我可以告诉您...",
        ]
        
        # 简单的响应选择逻辑
        import random
        response = random.choice(responses)
        
        # 模拟AI处理时间
        await asyncio.sleep(0.5)
        
        return response
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "service": "chat"}
        
        @self.app.post("/chat", response_model=ChatResponse)
        async def chat(request: ChatRequest):
            """处理对话请求"""
            try:
                # 生成或使用现有会话ID
                session_id = request.session_id or str(uuid.uuid4())
                
                # 保存用户消息到数据库
                await self.call_database_service("POST", "/conversations", {
                    "session_id": session_id,
                    "role": "user",
                    "content": request.message,
                    "attachments": request.attachments,
                    "metadata": {
                        "model": request.model,
                        "temperature": request.temperature,
                        "max_tokens": request.max_tokens
                    }
                })
                
                # 获取对话历史（用于上下文）
                history_response = await self.call_database_service(
                    "GET", f"/conversations/{session_id}", {"limit": 10}
                )
                history = history_response.get("data", [])
                
                # 生成AI响应
                ai_response = await self.simulate_ai_response(
                    request.message, session_id, request.model
                )
                
                # 保存AI响应到数据库
                await self.call_database_service("POST", "/conversations", {
                    "session_id": session_id,
                    "role": "assistant",
                    "content": ai_response,
                    "metadata": {
                        "model": request.model,
                        "context_length": len(history),
                        "response_time": 0.5  # 模拟响应时间
                    }
                })
                
                # 更新活跃会话
                self.active_sessions[session_id] = {
                    "last_activity": asyncio.get_event_loop().time(),
                    "message_count": len(history) + 2
                }
                
                return ChatResponse(
                    success=True,
                    session_id=session_id,
                    message=request.message,
                    response=ai_response,
                    metadata={
                        "model": request.model,
                        "context_length": len(history),
                        "session_message_count": len(history) + 2
                    }
                )
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")
        
        @self.app.get("/sessions/{session_id}/history")
        async def get_session_history(session_id: str, limit: int = 50):
            """获取会话历史"""
            try:
                return await self.call_database_service(
                    "GET", f"/conversations/{session_id}", {"limit": limit}
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")
        
        @self.app.get("/sessions")
        async def get_active_sessions():
            """获取活跃会话列表"""
            current_time = asyncio.get_event_loop().time()
            active = {}
            
            # 清理超过1小时未活动的会话
            for session_id, info in list(self.active_sessions.items()):
                if current_time - info["last_activity"] < 3600:  # 1小时
                    active[session_id] = info
                else:
                    del self.active_sessions[session_id]
            
            return {
                "success": True,
                "active_sessions": active,
                "count": len(active)
            }
        
        @self.app.delete("/sessions/{session_id}")
        async def clear_session(session_id: str):
            """清理会话（从活跃列表中移除）"""
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                return {"success": True, "message": f"会话 {session_id} 已清理"}
            else:
                raise HTTPException(status_code=404, detail="会话不存在")
        
        @self.app.websocket("/ws/{session_id}")
        async def websocket_chat(websocket: WebSocket, session_id: str):
            """WebSocket实时对话"""
            await websocket.accept()
            
            try:
                while True:
                    # 接收消息
                    data = await websocket.receive_json()
                    message = data.get("message", "")
                    
                    if not message:
                        await websocket.send_json({"error": "消息不能为空"})
                        continue
                    
                    # 保存用户消息
                    await self.call_database_service("POST", "/conversations", {
                        "session_id": session_id,
                        "role": "user",
                        "content": message,
                        "metadata": {"channel": "websocket"}
                    })
                    
                    # 生成AI响应
                    ai_response = await self.simulate_ai_response(message, session_id)
                    
                    # 保存AI响应
                    await self.call_database_service("POST", "/conversations", {
                        "session_id": session_id,
                        "role": "assistant",
                        "content": ai_response,
                        "metadata": {"channel": "websocket"}
                    })
                    
                    # 发送响应
                    await websocket.send_json({
                        "session_id": session_id,
                        "message": message,
                        "response": ai_response,
                        "timestamp": asyncio.get_event_loop().time()
                    })
                    
            except WebSocketDisconnect:
                print(f"WebSocket连接断开: {session_id}")
            except Exception as e:
                await websocket.send_json({"error": f"处理消息失败: {str(e)}"})
        
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print("🚀 AI对话微服务启动中...")
            
            # 检查数据库服务连接
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.database_service_url}/health")
                    if response.status_code == 200:
                        print("✅ 数据库服务连接正常")
                    else:
                        print("⚠️ 数据库服务连接异常")
            except Exception as e:
                print(f"❌ 无法连接数据库服务: {e}")
            
            print("✅ AI对话微服务启动完成")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """服务关闭事件"""
            print("🛑 AI对话微服务关闭中...")
            print("✅ AI对话微服务已关闭")
    
    def run(self, host: str = "127.0.0.1", port: int = 8002):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )


# 服务实例
service = ChatService()

if __name__ == "__main__":
    print("🤖 启动Yuan AI对话微服务")
    print("📍 服务地址: http://127.0.0.1:8002")
    print("📚 API文档: http://127.0.0.1:8002/docs")
    print("🔌 WebSocket: ws://127.0.0.1:8002/ws/{session_id}")
    
    service.run()
