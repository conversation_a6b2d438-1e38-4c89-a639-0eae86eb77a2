{% extends "base.html" %}

{% block title %}数据表 - Yuan数据库管理工具{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-table text-primary"></i>
        数据表管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-outline-success btn-custom me-2" onclick="showInsertModal()">
            <i class="fas fa-plus"></i>
            新增数据
        </button>
        <button class="btn btn-outline-primary btn-custom" onclick="refreshData()">
            <i class="fas fa-sync-alt"></i>
            刷新数据
        </button>
    </div>
</div>

<!-- 表选择和过滤 -->
<div class="row mb-4">
    <div class="col-md-3">
        <label class="form-label">选择表</label>
        <select class="form-select" id="tableSelect" onchange="loadTableData()">
            <option value="">请选择表...</option>
            {% for table in tables %}
            <option value="{{ table }}">{{ table }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-2">
        <label class="form-label">显示数量</label>
        <select class="form-select" id="limitSelect" onchange="loadTableData()">
            <option value="10">10条</option>
            <option value="50" selected>50条</option>
            <option value="100">100条</option>
            <option value="500">500条</option>
        </select>
    </div>
    <div class="col-md-3" id="sessionIdFilter" style="display: none;">
        <label class="form-label">会话ID</label>
        <input type="text" class="form-control" id="sessionIdInput" placeholder="输入会话ID...">
    </div>
    <div class="col-md-2" id="levelFilter" style="display: none;">
        <label class="form-label">日志级别</label>
        <select class="form-select" id="levelSelect">
            <option value="">全部</option>
            <option value="DEBUG">DEBUG</option>
            <option value="INFO">INFO</option>
            <option value="WARNING">WARNING</option>
            <option value="ERROR">ERROR</option>
            <option value="CRITICAL">CRITICAL</option>
        </select>
    </div>
    <div class="col-md-2" id="hoursFilter" style="display: none;">
        <label class="form-label">时间范围</label>
        <select class="form-select" id="hoursSelect">
            <option value="1">1小时</option>
            <option value="6">6小时</option>
            <option value="24" selected>24小时</option>
            <option value="168">7天</option>
        </select>
    </div>
</div>

<!-- 数据表格 -->
<div class="card shadow">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <span id="tableTitle">请选择要查看的表</span>
        </h6>
        <div>
            <button class="btn btn-sm btn-outline-info" onclick="exportData()" id="exportBtn" disabled>
                <i class="fas fa-download"></i>
                导出
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="dataTable">
                <thead class="table-light" id="tableHead">
                </thead>
                <tbody id="tableBody">
                    <tr>
                        <td class="text-center text-muted">请选择表以查看数据</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页信息 -->
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <small class="text-muted" id="recordInfo">共 0 条记录</small>
            </div>
        </div>
    </div>
</div>

<!-- 新增数据模态框 -->
<div class="modal fade" id="insertModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="insertForm">
                <p class="text-muted">请先选择表</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="insertData()" id="insertBtn" disabled>
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTable = '';
let currentData = [];

// 页面加载时检查URL参数
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const table = urlParams.get('table');
    if (table) {
        document.getElementById('tableSelect').value = table;
        loadTableData();
    }
});

// 加载表数据
function loadTableData() {
    const table = document.getElementById('tableSelect').value;
    if (!table) {
        resetTable();
        return;
    }
    
    currentTable = table;
    const limit = document.getElementById('limitSelect').value;
    
    // 显示/隐藏过滤器
    showFilters(table);
    
    // 构建查询参数
    const queryData = {
        table: table,
        limit: parseInt(limit)
    };
    
    // 添加特定过滤器
    if (table === 'conversations') {
        const sessionId = document.getElementById('sessionIdInput').value.trim();
        if (sessionId) {
            queryData.session_id = sessionId;
        }
    } else if (table === 'logs' || table === 'system_metrics') {
        const hours = document.getElementById('hoursSelect').value;
        queryData.hours = parseInt(hours);
        
        if (table === 'logs') {
            const level = document.getElementById('levelSelect').value;
            if (level) {
                queryData.level = level;
            }
        }
    }
    
    showLoading();
    
    fetch('/api/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            showAlert('danger', data.error);
            resetTable();
        } else {
            displayTableData(data.data);
            updateRecordInfo(data.count);
            document.getElementById('exportBtn').disabled = false;
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('danger', '加载数据失败: ' + error.message);
        resetTable();
    });
}

// 显示相应的过滤器
function showFilters(table) {
    // 隐藏所有过滤器
    document.getElementById('sessionIdFilter').style.display = 'none';
    document.getElementById('levelFilter').style.display = 'none';
    document.getElementById('hoursFilter').style.display = 'none';
    
    // 显示对应的过滤器
    if (table === 'conversations') {
        document.getElementById('sessionIdFilter').style.display = 'block';
    } else if (table === 'logs') {
        document.getElementById('levelFilter').style.display = 'block';
        document.getElementById('hoursFilter').style.display = 'block';
    } else if (table === 'system_metrics') {
        document.getElementById('hoursFilter').style.display = 'block';
    }
}

// 显示表数据
function displayTableData(data) {
    currentData = data;
    const tableHead = document.getElementById('tableHead');
    const tableBody = document.getElementById('tableBody');
    const tableTitle = document.getElementById('tableTitle');
    
    tableTitle.textContent = `${currentTable} 表数据`;
    
    if (!data || data.length === 0) {
        tableHead.innerHTML = '';
        tableBody.innerHTML = '<tr><td class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    // 生成表头
    const headers = Object.keys(data[0]);
    tableHead.innerHTML = `
        <tr>
            ${headers.map(header => `<th>${header}</th>`).join('')}
        </tr>
    `;
    
    // 生成表体
    tableBody.innerHTML = '';
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = headers.map(header => {
            let value = row[header];
            if (value === null || value === undefined) {
                value = '<span class="text-muted">NULL</span>';
            } else if (typeof value === 'string' && value.length > 100) {
                value = `<span title="${value}">${value.substring(0, 100)}...</span>`;
            } else if (typeof value === 'object') {
                value = `<code>${JSON.stringify(value)}</code>`;
            }
            return `<td>${value}</td>`;
        }).join('');
        tableBody.appendChild(tr);
    });
}

// 重置表格
function resetTable() {
    currentTable = '';
    currentData = [];
    document.getElementById('tableHead').innerHTML = '';
    document.getElementById('tableBody').innerHTML = '<tr><td class="text-center text-muted">请选择表以查看数据</td></tr>';
    document.getElementById('tableTitle').textContent = '请选择要查看的表';
    document.getElementById('recordInfo').textContent = '共 0 条记录';
    document.getElementById('exportBtn').disabled = true;
    
    // 隐藏所有过滤器
    showFilters('');
}

// 更新记录信息
function updateRecordInfo(count) {
    document.getElementById('recordInfo').textContent = `共 ${formatNumber(count)} 条记录`;
}

// 刷新数据
function refreshData() {
    if (currentTable) {
        loadTableData();
    }
}

// 显示新增数据模态框
function showInsertModal() {
    if (!currentTable) {
        showAlert('warning', '请先选择表');
        return;
    }
    
    generateInsertForm();
    new bootstrap.Modal(document.getElementById('insertModal')).show();
}

// 生成插入表单
function generateInsertForm() {
    const insertForm = document.getElementById('insertForm');
    const insertBtn = document.getElementById('insertBtn');
    
    if (currentTable === 'conversations') {
        insertForm.innerHTML = `
            <div class="mb-3">
                <label class="form-label">会话ID *</label>
                <input type="text" class="form-control" id="insert_session_id" required>
            </div>
            <div class="mb-3">
                <label class="form-label">角色 *</label>
                <select class="form-select" id="insert_role" required>
                    <option value="">请选择...</option>
                    <option value="user">user</option>
                    <option value="assistant">assistant</option>
                    <option value="system">system</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">内容 *</label>
                <textarea class="form-control" id="insert_content" rows="4" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">元数据 (JSON格式)</label>
                <textarea class="form-control" id="insert_metadata" rows="2" placeholder='{"key": "value"}'></textarea>
            </div>
        `;
        insertBtn.disabled = false;
    } else if (currentTable === 'logs') {
        insertForm.innerHTML = `
            <div class="mb-3">
                <label class="form-label">日志级别 *</label>
                <select class="form-select" id="insert_level" required>
                    <option value="">请选择...</option>
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                    <option value="CRITICAL">CRITICAL</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">模块名称 *</label>
                <input type="text" class="form-control" id="insert_module" required>
            </div>
            <div class="mb-3">
                <label class="form-label">日志消息 *</label>
                <textarea class="form-control" id="insert_message" rows="3" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">函数名称</label>
                <input type="text" class="form-control" id="insert_function_name">
            </div>
            <div class="mb-3">
                <label class="form-label">行号</label>
                <input type="number" class="form-control" id="insert_line_number">
            </div>
            <div class="mb-3">
                <label class="form-label">异常信息</label>
                <textarea class="form-control" id="insert_exception_info" rows="2"></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">额外数据 (JSON格式)</label>
                <textarea class="form-control" id="insert_extra_data" rows="2" placeholder='{"key": "value"}'></textarea>
            </div>
        `;
        insertBtn.disabled = false;
    } else {
        insertForm.innerHTML = `<p class="text-muted">暂不支持向 ${currentTable} 表插入数据</p>`;
        insertBtn.disabled = true;
    }
}

// 插入数据
function insertData() {
    const insertData = { table: currentTable };
    
    if (currentTable === 'conversations') {
        insertData.session_id = document.getElementById('insert_session_id').value.trim();
        insertData.role = document.getElementById('insert_role').value;
        insertData.content = document.getElementById('insert_content').value.trim();
        insertData.metadata = document.getElementById('insert_metadata').value.trim();
        
        if (!insertData.session_id || !insertData.role || !insertData.content) {
            showAlert('warning', '请填写所有必填字段');
            return;
        }
    } else if (currentTable === 'logs') {
        insertData.level = document.getElementById('insert_level').value;
        insertData.module = document.getElementById('insert_module').value.trim();
        insertData.message = document.getElementById('insert_message').value.trim();
        insertData.function_name = document.getElementById('insert_function_name').value.trim() || null;
        insertData.line_number = document.getElementById('insert_line_number').value || null;
        insertData.exception_info = document.getElementById('insert_exception_info').value.trim() || null;
        insertData.extra_data = document.getElementById('insert_extra_data').value.trim();
        
        if (!insertData.level || !insertData.module || !insertData.message) {
            showAlert('warning', '请填写所有必填字段');
            return;
        }
    }
    
    showLoading();
    
    fetch('/api/insert', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(insertData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('insertModal')).hide();
            loadTableData(); // 刷新数据
        } else {
            showAlert('danger', data.error);
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('danger', '插入数据失败: ' + error.message);
    });
}

// 导出数据
function exportData() {
    if (!currentTable || currentData.length === 0) {
        showAlert('warning', '没有数据可导出');
        return;
    }
    
    const limit = document.getElementById('limitSelect').value;
    
    fetch(`/api/export/${currentTable}?limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', data.error);
            } else {
                // 创建下载链接
                const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${currentTable}_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showAlert('success', `成功导出 ${data.count} 条记录`);
            }
        })
        .catch(error => {
            showAlert('danger', '导出失败: ' + error.message);
        });
}
</script>
{% endblock %}
