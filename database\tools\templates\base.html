<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Yuan数据库管理工具{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .btn-custom {
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
        }
        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
        .alert-custom {
            border-radius: 10px;
            border: none;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-database"></i>
                            Yuan数据库
                        </h4>
                        <small class="text-white-50">管理工具</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="fas fa-home"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'tables' %}active{% endif %}" href="{{ url_for('tables') }}">
                                <i class="fas fa-table"></i>
                                数据表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'query' %}active{% endif %}" href="{{ url_for('query') }}">
                                <i class="fas fa-search"></i>
                                查询工具
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showMaintenanceModal()">
                                <i class="fas fa-tools"></i>
                                维护工具
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <div class="text-center">
                        <small class="text-white-50">
                            <i class="fas fa-clock"></i>
                            <span id="current-time"></span>
                        </small>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- 维护工具模态框 -->
    <div class="modal fade" id="maintenanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">数据库维护</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">维护操作</label>
                        <select class="form-select" id="maintenanceOperation">
                            <option value="reindex">重建索引</option>
                            <option value="analyze">分析表统计</option>
                            <option value="integrity">完整性检查</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">清理旧数据</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="cleanupDays" value="30" min="1">
                            <span class="input-group-text">天前</span>
                            <button class="btn btn-warning" onclick="cleanupData()">清理</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="performMaintenance()">执行维护</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载提示 -->
    <div class="position-fixed top-50 start-50 translate-middle loading" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }
        setInterval(updateTime, 1000);
        updateTime();
        
        // 显示加载动画
        function showLoading() {
            document.getElementById('loadingSpinner').classList.add('show');
        }
        
        // 隐藏加载动画
        function hideLoading() {
            document.getElementById('loadingSpinner').classList.remove('show');
        }
        
        // 显示维护模态框
        function showMaintenanceModal() {
            new bootstrap.Modal(document.getElementById('maintenanceModal')).show();
        }
        
        // 执行维护操作
        function performMaintenance() {
            const operation = document.getElementById('maintenanceOperation').value;
            showLoading();
            
            fetch('/api/maintenance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ operation: operation })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.message);
                } else {
                    showAlert('danger', data.error);
                }
                bootstrap.Modal.getInstance(document.getElementById('maintenanceModal')).hide();
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '维护操作失败: ' + error.message);
            });
        }
        
        // 清理数据
        function cleanupData() {
            const days = document.getElementById('cleanupDays').value;
            if (!confirm(`确认清理 ${days} 天前的旧数据吗？此操作不可恢复！`)) {
                return;
            }
            
            showLoading();
            
            fetch('/api/cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ days: parseInt(days) })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('success', data.message);
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                hideLoading();
                showAlert('danger', '清理操作失败: ' + error.message);
            });
        }
        
        // 显示提示信息
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-custom`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 格式化数字
        function formatNumber(num) {
            return num.toLocaleString();
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
