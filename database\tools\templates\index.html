{% extends "base.html" %}

{% block title %}仪表板 - Yuan数据库管理工具{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt text-primary"></i>
        数据库仪表板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-outline-primary btn-custom" onclick="refreshStatus()">
            <i class="fas fa-sync-alt"></i>
            刷新状态
        </button>
    </div>
</div>

<!-- 状态卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            数据库文件
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="dbFileSize">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            总记录数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalRecords">
                            加载中...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            数据表数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ tables|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-table fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="dbStatus">
                            <span class="status-badge bg-success text-white">运行中</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-heartbeat fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 表统计图表和详情 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie"></i>
                    表记录分布
                </h6>
            </div>
            <div class="card-body">
                <canvas id="tableStatsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list-ul"></i>
                    表详细统计
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>表名</th>
                                <th>记录数</th>
                                <th>占比</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableStatsBody">
                            <tr>
                                <td colspan="4" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据库信息 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i>
                    数据库信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>文件路径:</strong> <code id="dbPath">加载中...</code></p>
                        <p><strong>文件大小:</strong> <span id="dbSize">加载中...</span></p>
                        <p><strong>最后更新:</strong> <span id="lastUpdate">加载中...</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>支持的表:</strong></p>
                        <ul class="list-unstyled">
                            {% for table in tables %}
                            <li><i class="fas fa-table text-muted"></i> {{ table }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let tableStatsChart = null;

// 页面加载时获取状态
document.addEventListener('DOMContentLoaded', function() {
    refreshStatus();
});

// 刷新状态
function refreshStatus() {
    showLoading();
    
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            updateStatusDisplay(data);
            updateTableStats(data.table_stats);
            updateChart(data.table_stats);
        })
        .catch(error => {
            hideLoading();
            showAlert('danger', '获取状态失败: ' + error.message);
        });
}

// 更新状态显示
function updateStatusDisplay(data) {
    document.getElementById('dbFileSize').textContent = formatFileSize(data.file_info.size_mb * 1024 * 1024);
    document.getElementById('totalRecords').textContent = formatNumber(data.total_records);
    document.getElementById('dbPath').textContent = data.file_info.path;
    document.getElementById('dbSize').textContent = formatFileSize(data.file_info.size_mb * 1024 * 1024);
    document.getElementById('lastUpdate').textContent = new Date(data.timestamp).toLocaleString();
}

// 更新表统计
function updateTableStats(stats) {
    const tbody = document.getElementById('tableStatsBody');
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    
    tbody.innerHTML = '';
    
    for (const [table, count] of Object.entries(stats)) {
        const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <i class="fas fa-table text-muted"></i>
                ${table}
            </td>
            <td>${formatNumber(count)}</td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" role="progressbar" style="width: ${percentage}%">
                        ${percentage}%
                    </div>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewTable('${table}')">
                    <i class="fas fa-eye"></i>
                    查看
                </button>
            </td>
        `;
        tbody.appendChild(row);
    }
}

// 更新图表
function updateChart(stats) {
    const ctx = document.getElementById('tableStatsChart').getContext('2d');
    
    if (tableStatsChart) {
        tableStatsChart.destroy();
    }
    
    const labels = Object.keys(stats);
    const data = Object.values(stats);
    const colors = [
        '#FF6384',
        '#36A2EB',
        '#FFCE56',
        '#4BC0C0',
        '#9966FF',
        '#FF9F40'
    ];
    
    tableStatsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatNumber(context.parsed);
                            const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 查看表数据
function viewTable(tableName) {
    window.location.href = `/tables?table=${tableName}`;
}
</script>
{% endblock %}
