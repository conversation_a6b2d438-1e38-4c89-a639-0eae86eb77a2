#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库开发工具模块
提供命令行和Web可视化数据库管理工具
"""

__version__ = '1.0.0'
__author__ = 'Yuan Project'
__description__ = '数据库开发工具 - 命令行和Web可视化管理工具'

# 工具列表
AVAILABLE_TOOLS = {
    'cli': {
        'name': '命令行数据库工具',
        'description': '强大的命令行数据库管理工具，支持脚本化操作',
        'module': 'cli_tool',
        'usage': 'python -m database.tools.cli_tool --help'
    },
    'web': {
        'name': 'Web可视化工具',
        'description': '基于Web的数据库可视化管理界面',
        'module': 'web_tool',
        'usage': 'python -m database.tools.web_tool'
    }
}

def list_tools():
    """列出所有可用工具"""
    print("Yuan项目数据库开发工具")
    print("=" * 40)
    
    for tool_id, info in AVAILABLE_TOOLS.items():
        print(f"\n{tool_id.upper()}工具:")
        print(f"  名称: {info['name']}")
        print(f"  描述: {info['description']}")
        print(f"  使用: {info['usage']}")

if __name__ == "__main__":
    list_tools()
