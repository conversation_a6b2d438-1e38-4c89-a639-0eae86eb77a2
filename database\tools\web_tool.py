#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web可视化数据库管理工具
提供基于Web的数据库管理界面
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for
import threading

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from database.core import DatabaseCore
from database.models import TABLE_NAMES


class DatabaseWebTool:
    """Web数据库管理工具"""
    
    def __init__(self):
        self.db = DatabaseCore()
        self.app = Flask(__name__, 
                        template_folder=str(Path(__file__).parent / 'templates'),
                        static_folder=str(Path(__file__).parent / 'static'))
        self.setup_routes()
        self._db_initialized = False
    
    async def initialize_db(self):
        """初始化数据库"""
        if not self._db_initialized:
            await self.db.initialize()
            self._db_initialized = True
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('index.html', tables=TABLE_NAMES)
        
        @self.app.route('/api/status')
        def api_status():
            """获取数据库状态"""
            async def get_status():
                await self.initialize_db()
                
                # 数据库文件信息
                db_path = self.db.db_path
                file_info = {
                    'path': str(db_path),
                    'exists': db_path.exists(),
                    'size_mb': db_path.stat().st_size / (1024 * 1024) if db_path.exists() else 0
                }
                
                # 表统计信息
                stats = await self.db.get_table_stats()
                
                return {
                    'file_info': file_info,
                    'table_stats': stats,
                    'total_records': sum(stats.values()),
                    'timestamp': datetime.now().isoformat()
                }
            
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(get_status())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/api/query', methods=['POST'])
        def api_query():
            """执行查询"""
            data = request.get_json()
            table = data.get('table')
            sql = data.get('sql')
            limit = data.get('limit', 50)
            session_id = data.get('session_id')
            level = data.get('level')
            hours = data.get('hours', 24)
            
            async def execute_query():
                await self.initialize_db()
                
                if sql:
                    # 自定义SQL查询
                    if not sql.upper().strip().startswith('SELECT'):
                        return {'error': '只支持SELECT查询'}
                    result = await self.db.execute_query(sql)
                elif table:
                    # 表查询
                    if table not in TABLE_NAMES:
                        return {'error': f'无效的表名: {table}'}
                    
                    if table == 'conversations':
                        if session_id:
                            result = await self.db.get_conversation_history(session_id, limit)
                        else:
                            query = f"SELECT * FROM conversations ORDER BY timestamp DESC LIMIT {limit}"
                            result = await self.db.execute_query(query)
                    elif table == 'system_metrics':
                        result = await self.db.get_system_metrics(hours)
                        result = result[:limit]
                    elif table == 'logs':
                        result = await self.db.get_logs(level=level, hours=hours, limit=limit)
                    else:
                        query = f"SELECT * FROM {table} ORDER BY id DESC LIMIT {limit}"
                        result = await self.db.execute_query(query)
                else:
                    return {'error': '必须指定表名或SQL查询'}
                
                return {'data': result, 'count': len(result)}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(execute_query())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/api/insert', methods=['POST'])
        def api_insert():
            """插入数据"""
            data = request.get_json()
            table = data.get('table')
            
            async def insert_data():
                await self.initialize_db()
                
                try:
                    if table == 'conversations':
                        attachments = json.loads(data.get('attachments', '[]')) if data.get('attachments') else None
                        metadata = json.loads(data.get('metadata', '{}')) if data.get('metadata') else None
                        await self.db.save_conversation(
                            data['session_id'],
                            data['role'],
                            data['content'],
                            attachments,
                            metadata
                        )
                    elif table == 'logs':
                        await self.db.save_log(
                            data['level'],
                            data['module'],
                            data['message'],
                            data.get('function_name'),
                            data.get('line_number'),
                            data.get('exception_info'),
                            json.loads(data.get('extra_data', '{}')) if data.get('extra_data') else None
                        )
                    else:
                        return {'error': f'不支持插入到表: {table}'}
                    
                    # 等待批量写入
                    await asyncio.sleep(1)
                    return {'success': True, 'message': '数据插入成功'}
                    
                except Exception as e:
                    return {'error': f'插入失败: {str(e)}'}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(insert_data())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/api/export/<table>')
        def api_export(table):
            """导出数据"""
            limit = request.args.get('limit', 1000, type=int)
            
            async def export_data():
                await self.initialize_db()
                
                if table not in TABLE_NAMES:
                    return {'error': f'无效的表名: {table}'}
                
                query = f"SELECT * FROM {table} ORDER BY id DESC LIMIT {limit}"
                result = await self.db.execute_query(query)
                
                return {
                    'data': result,
                    'count': len(result),
                    'table': table,
                    'exported_at': datetime.now().isoformat()
                }
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(export_data())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/api/cleanup', methods=['POST'])
        def api_cleanup():
            """清理旧数据"""
            data = request.get_json()
            days = data.get('days', 30)
            
            async def cleanup_data():
                await self.initialize_db()
                try:
                    await self.db.cleanup_old_data(days)
                    return {'success': True, 'message': f'已清理 {days} 天前的旧数据'}
                except Exception as e:
                    return {'error': f'清理失败: {str(e)}'}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(cleanup_data())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/api/maintenance', methods=['POST'])
        def api_maintenance():
            """数据库维护"""
            data = request.get_json()
            operation = data.get('operation')
            
            async def maintenance():
                await self.initialize_db()
                try:
                    if operation == 'reindex':
                        await self.db.execute_query("REINDEX")
                        message = "索引重建完成"
                    elif operation == 'analyze':
                        await self.db.execute_query("ANALYZE")
                        message = "表统计信息分析完成"
                    elif operation == 'integrity':
                        result = await self.db.execute_query("PRAGMA integrity_check")
                        if result and result[0].get('integrity_check') == 'ok':
                            message = "数据库完整性检查通过"
                        else:
                            return {'error': '数据库完整性检查发现问题', 'details': result}
                    else:
                        return {'error': f'不支持的维护操作: {operation}'}
                    
                    return {'success': True, 'message': message}
                except Exception as e:
                    return {'error': f'维护操作失败: {str(e)}'}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(maintenance())
                return jsonify(result)
            finally:
                loop.close()
        
        @self.app.route('/tables')
        def tables():
            """表格查看页面"""
            return render_template('tables.html', tables=TABLE_NAMES)
        
        @self.app.route('/query')
        def query():
            """查询页面"""
            return render_template('query.html', tables=TABLE_NAMES)
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行Web应用"""
        print(f"Yuan数据库Web管理工具启动")
        print(f"访问地址: http://{host}:{port}")
        print("按 Ctrl+C 停止服务")
        
        self.app.run(host=host, port=port, debug=debug, threaded=True)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Yuan项目数据库Web管理工具')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    web_tool = DatabaseWebTool()
    try:
        web_tool.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n服务已停止")


if __name__ == "__main__":
    main()
