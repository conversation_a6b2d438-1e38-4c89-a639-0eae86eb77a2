# Yuan项目微服务架构设计

## 总体架构概述

Yuan项目采用**基于服务注册中心的微服务架构**：
- **服务注册中心**：统一的服务注册、发现和健康监控
- **数据库分离**：每个服务拥有独立的数据库，完全数据隔离
- **纯API通信**：服务间通过RESTful API进行通信，零代码依赖
- **动态服务管理**：支持服务的动态注册、发现、扩缩容

实现真正的微服务架构：服务自治、数据隔离、动态管理。

## 微服务架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Web交互界面层                              │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/WebSocket
┌─────────────────────▼───────────────────────────────────────┐
│              Service Registry (8500)                        │
│                   服务注册中心                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  服务注册 | 服务发现 | 健康检查 | 负载均衡 | 故障转移    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────┬─────────┬─────────┬─────────┬─────────┬─────────────────┘
      │         │         │         │         │
      │ 注册/发现 │ 注册/发现 │ 注册/发现 │ 注册/发现 │
      │         │         │         │         │
┌─────▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐
│ Chat    │ │ Log   │ │Memory │ │ File  │ │Config │
│Service  │ │Service│ │Service│ │Service│ │Service│
│ (8002)  │ │(8003) │ │(8004) │ │(8005) │ │(8006) │
└─────────┘ └───────┘ └───────┘ └───────┘ └───────┘
      │         │         │         │         │
      │ API调用  │ API调用  │ API调用  │ API调用  │
      │         │         │         │         │
┌─────▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐
│ chat_   │ │ log_  │ │memory_│ │ file_ │ │config_│
│service  │ │service│ │service│ │service│ │service│
│  .db    │ │  .db  │ │  .db  │ │  .db  │ │  .db  │
└─────────┘ └───────┘ └───────┘ └───────┘ └───────┘
```

## 核心设计原则

### 1. 服务自治原则
- **独立进程**：每个服务运行在独立的进程中
- **独立数据库**：每个服务拥有专属的数据库，完全数据隔离
- **独立部署**：服务可以独立开发、测试、部署和扩展
- **故障隔离**：单个服务故障不影响其他服务运行

### 2. 动态服务管理
- **自动注册**：服务启动时自动注册到服务注册中心
- **服务发现**：通过服务名称动态发现其他服务实例
- **健康监控**：定期心跳检测，自动清理失效服务
- **负载均衡**：自动选择健康的服务实例进行调用

### 3. 纯API通信
- **零代码依赖**：服务间通过RESTful API通信，无代码耦合
- **标准化接口**：统一的API设计规范和错误处理
- **异步调用**：支持异步API调用，提高系统性能
- **协议无关**：可以支持HTTP、gRPC等多种通信协议

## 微服务规划

### 核心服务

#### Service Registry (8500)
**功能**：服务注册中心
- 服务注册和注销管理
- 服务发现和实例查询
- 健康检查和心跳监控
- 自动清理失效服务实例
- 负载均衡和故障转移

**数据存储**：内存存储（服务信息）
**API接口**：
- `POST /register` - 服务注册
- `GET /discover/{service_name}` - 服务发现
- `POST /heartbeat` - 心跳更新
- `DELETE /unregister/{service_id}` - 服务注销

#### Chat Service (8002)
**功能**：AI对话处理服务
- 对话生成和处理
- 会话状态管理
- 上下文维护
- 对话历史查询

**独立数据库**：`data/chat_service.db`
- `conversations` - 对话记录表
- `chat_sessions` - 会话信息表

**API接口**：
- `POST /chat` - 发送对话
- `GET /sessions/{session_id}/history` - 获取会话历史
- `GET /sessions` - 获取会话列表
- `GET /stats` - 获取对话统计

#### Log Service (8003)
**功能**：日志记录和管理服务
- 日志收集和存储
- 日志查询和过滤
- 日志统计和分析
- 日志清理和归档

**独立数据库**：`data/log_service.db`
- `logs` - 日志记录表
- `log_stats` - 日志统计表

**API接口**：
- `POST /logs` - 记录日志
- `GET /logs` - 查询日志
- `GET /logs/stats` - 获取日志统计
- `DELETE /logs/cleanup` - 清理旧日志

### 扩展服务（规划中）

#### Memory Service (8004)
**功能**：记忆管理服务
- 长期记忆存储和检索
- 语义索引和搜索
- 记忆关联和图谱
- 记忆自动形成

**独立数据库**：`data/memory_service.db`
- `memories` - 记忆记录表
- `memory_relations` - 记忆关联表

#### File Service (8005)
**功能**：文件管理服务
- 文件上传和下载
- 文件存储和组织
- 文件元数据管理
- 文件安全和权限

**独立数据库**：`data/file_service.db`
- `files` - 文件信息表
- `file_permissions` - 文件权限表

#### Config Service (8006)
**功能**：配置管理服务
- 系统配置管理
- 动态配置更新
- 配置版本控制
- 配置分发和同步

**独立数据库**：`data/config_service.db`
- `configurations` - 配置项表
- `config_history` - 配置历史表

## 服务通信协议

### 服务注册流程
```
1. 服务启动 → 初始化BaseService
2. 自动向Service Registry注册
   - 发送服务信息（名称、地址、端口、健康检查URL）
   - 获得唯一service_id
3. 开始定期心跳（每15秒）
4. 服务正常运行，接受API请求
```

### 服务发现和调用流程
```
1. 服务需要调用其他服务
2. 通过Service Registry发现目标服务
   - 查询：GET /discover/{service_name}
   - 获得健康服务实例列表
3. 选择服务实例（负载均衡）
4. 直接API调用目标服务
5. 处理响应或错误
```

### 对话处理完整流程
```
1. 用户输入 → Chat Service
2. Chat Service处理对话请求
3. 生成AI回复
4. 保存对话到本地数据库（chat_service.db）
5. 调用Log Service记录日志
   - API调用：POST /logs
6. 返回回复给用户
```

### 服务间API调用规范
```
1. 服务发现：await self.discover_service("target_service")
2. API调用：await self.call_service("target_service", "/endpoint", "POST", data)
3. 错误处理：自动重试和故障转移
4. 日志记录：记录调用结果和性能指标
```

## 技术实现规范

### 微服务基础规范
- **基础类**：所有服务必须继承`BaseService`类
- **自动注册**：服务启动时自动注册到Service Registry
- **健康检查**：必须实现`/health`端点
- **标准端口**：每个服务使用固定端口号
- **异步框架**：基于FastAPI和asyncio实现

### 数据库设计规范
- **独立数据库**：每个服务使用独立的SQLite数据库
- **命名规范**：数据库文件命名为`{service_name}_service.db`
- **存储位置**：统一存储在`data/`目录下
- **ORM框架**：使用SQLAlchemy异步ORM
- **数据隔离**：服务间不得直接访问其他服务的数据库

### API设计规范
- **RESTful风格**：遵循REST API设计原则
- **统一响应格式**：成功响应包含`success`、`data`、`message`字段
- **错误处理**：使用HTTP状态码和标准错误格式
- **文档生成**：自动生成OpenAPI文档（/docs端点）
- **CORS支持**：支持跨域请求

## 服务开发规范

### 新增微服务步骤
1. **创建服务目录**：在项目根目录创建`{service_name}_service/`目录
2. **继承BaseService**：创建服务类继承`BaseService`
3. **定义数据模型**：使用SQLAlchemy定义数据库模型
4. **实现API接口**：定义RESTful API端点
5. **服务注册**：服务启动时自动注册到Service Registry
6. **编写测试**：创建单元测试和集成测试

### 服务注册信息规范
```python
# 服务注册时必须提供的信息
{
    "service_name": "服务名称（小写，下划线分隔）",
    "host": "服务IP地址",
    "port": "服务端口号",
    "health_check_url": "健康检查URL",
    "metadata": {
        "version": "服务版本号",
        "description": "服务描述",
        "capabilities": ["服务能力列表"]
    }
}
```

### 心跳和健康检查规范
- **心跳频率**：每15秒发送一次心跳
- **健康检查**：必须实现`GET /health`端点
- **超时设置**：30秒无心跳标记为不健康，5分钟无心跳自动清理
- **状态报告**：心跳中包含服务状态和元数据更新

## 开发实施路径

### 第一阶段：核心基础设施 ✅
1. ✅ 实现Service Registry（服务注册中心）
2. ✅ 创建BaseService基础类
3. ✅ 建立服务注册、发现、心跳机制
4. ✅ 实现服务间API调用框架

### 第二阶段：核心业务服务 ✅
1. ✅ Chat Service（AI对话服务）
2. ✅ Log Service（日志服务）
3. ✅ 独立数据库设计和实现
4. ✅ 服务间通信验证

### 第三阶段：扩展业务服务 🚧
1. 🚧 Memory Service（记忆管理服务）
2. 🚧 File Service（文件管理服务）
3. 🚧 Config Service（配置管理服务）
4. 🚧 完善服务间协作机制

### 第四阶段：生产化部署 📋
1. 📋 容器化部署（Docker）
2. 📋 服务编排（Docker Compose/Kubernetes）
3. 📋 监控和日志聚合
4. 📋 性能优化和调优

## 微服务架构优势

✅ **服务自治**：每个服务独立开发、测试、部署和扩展
✅ **数据隔离**：每个服务拥有独立数据库，完全数据安全
✅ **故障隔离**：单个服务故障不影响其他服务运行
✅ **技术自由**：每个服务可以使用不同的技术栈
✅ **动态管理**：支持服务的动态注册、发现和扩缩容
✅ **负载均衡**：自动选择健康服务实例，提高可用性
✅ **零代码耦合**：服务间通过API通信，无代码依赖
✅ **标准化接口**：统一的RESTful API设计规范
✅ **可观测性**：完整的日志记录和服务监控
✅ **易于测试**：服务独立，便于单元测试和集成测试

## 适用场景

### ✅ 推荐使用场景
- 大型项目或复杂业务逻辑
- 多团队协作开发
- 需要独立扩展不同模块
- 对高可用性有要求
- 需要支持多种技术栈

### ⚠️ 需要考虑的挑战
- 网络延迟和通信开销
- 分布式系统复杂性
- 数据一致性处理
- 运维和监控复杂度
- 调试和问题排查难度

### 🚫 不推荐场景
- 小型项目或个人开发
- 简单CRUD应用
- 对延迟极度敏感的应用
- 团队规模较小
- 资源有限的环境
