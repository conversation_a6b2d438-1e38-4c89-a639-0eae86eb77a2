# Yuan项目运维工具

Yuan项目的数据库管理和运维工具集合。

## 工具列表

### 1. 数据库管理工具 (database_manager.py)

交互式数据库管理工具，提供完整的数据库操作界面。

**启动方式**：
```bash
cd tools
python database_manager.py
```

**功能特性**：
- 📊 列出所有数据库
- 🔍 查看数据库详细信息
- 📋 查看表数据
- 💾 备份数据库
- 🧹 清理数据库（VACUUM）

### 2. 数据库命令行工具 (db_cli.py)

快速的命令行数据库操作工具。

**基本用法**：
```bash
cd tools
python db_cli.py <命令> [参数]
```

**可用命令**：

#### 列出所有数据库
```bash
python db_cli.py list
```

#### 查看数据库信息
```bash
python db_cli.py info <服务名>
```
示例：
```bash
python db_cli.py info chat
```

#### 显示表数据
```bash
python db_cli.py show <服务名> <表名> [选项]
```
选项：
- `-l, --limit`: 显示记录数（默认10）
- `-w, --where`: WHERE条件

示例：
```bash
python db_cli.py show chat conversations
python db_cli.py show chat messages -l 20
python db_cli.py show chat messages -w "role='user'"
```

#### 备份数据库
```bash
python db_cli.py backup <服务名> [选项]
```
选项：
- `-d, --dir`: 备份目录（默认backups）

示例：
```bash
python db_cli.py backup chat
python db_cli.py backup chat -d /path/to/backup
```

#### 清理数据库
```bash
python db_cli.py vacuum <服务名> [选项]
```
选项：
- `-f, --force`: 强制执行，不询问确认

示例：
```bash
python db_cli.py vacuum chat
python db_cli.py vacuum chat -f
```

## 使用场景

### 日常运维
1. **检查数据库状态**：
   ```bash
   python db_cli.py list
   python db_cli.py info chat
   ```

2. **查看最新对话**：
   ```bash
   python db_cli.py show chat conversations -l 5
   python db_cli.py show chat messages -l 10
   ```

3. **定期备份**：
   ```bash
   python db_cli.py backup chat
   ```

### 故障排查
1. **查看错误消息**：
   ```bash
   python db_cli.py show chat messages -w "content LIKE '%错误%'"
   ```

2. **检查特定用户的对话**：
   ```bash
   python db_cli.py show chat conversations -w "user_id='user123'"
   ```

3. **查看数据库统计**：
   ```bash
   python db_cli.py info chat
   ```

### 数据维护
1. **清理数据库空间**：
   ```bash
   python db_cli.py vacuum chat
   ```

2. **备份重要数据**：
   ```bash
   python db_cli.py backup chat -d important_backups
   ```

## 注意事项

### 安全提醒
- 🔒 备份操作是安全的，不会影响原数据库
- ⚠️ VACUUM操作会锁定数据库，建议在服务停止时执行
- 🚫 不要直接修改数据库文件，使用API接口进行数据操作

### 性能建议
- 📊 大表查询时使用`-l`限制返回记录数
- 🔍 使用`-w`条件过滤数据，提高查询效率
- 💾 定期备份数据库，避免数据丢失

### 故障处理
- 如果工具无法找到数据库，检查`data/`目录是否存在
- 如果备份失败，检查磁盘空间和权限
- 如果VACUUM失败，可能是数据库正在被使用

## 扩展开发

### 添加新功能
1. 在`DatabaseManager`类中添加新方法
2. 在`db_cli.py`中添加对应的命令处理函数
3. 更新帮助文档

### 支持新数据库
工具会自动发现`data/`目录下的所有`.db`文件，无需额外配置。

## 相关文档
- [开发规范](../docs/开发规范.md)
- [服务端口分配](../docs/服务端口分配.md)
- [项目架构设计](../docs/项目设计/)
